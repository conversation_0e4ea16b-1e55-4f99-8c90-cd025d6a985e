openai>=1.0.0
python-dotenv>=1.0.0
requests>=2.31.0
pillow>=10.0.0
moviepy==1.0.3
pydub>=0.25.1
flask>=2.0.0
flask-cors>=3.0.10
google-api-python-client>=2.100.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=1.1.0
scipy>=1.10.0
redis>=5.0.1
faiss-cpu>=1.7.4
librosa>=0.10.1

prometheus-client>=0.17.1
ffmpeg-python>=0.2.0
sentence-transformers>=2.2.2

# Intelligent highlights extraction dependencies
keybert>=0.8.0
transformers>=4.21.0
torch>=1.12.0
scikit-learn>=1.1.0

# Advanced highlight extraction dependencies
spacy>=3.6.0
vaderSentiment>=3.3.2
ruptures>=1.1.8
changeforest>=0.1.0

tf-keras==2.19.0

numpy==1.26.4
tensorflow==2.19.0


# Advanced Face Detection Dependencies
# Install these for optimal face detection accuracy

# High Accuracy Face Detection
insightface>=0.7.3          # SCRFD - High accuracy face detection
onnxruntime>=1.15.0         # Required for InsightFace (CPU)

# Fast and Reliable Face Detection
mediapipe==0.10.21           # Google's MediaPipe - Fast and reliable

# GPU acceleration dependencies (optional but recommended)
# Uncomment the following lines for GPU acceleration:
# onnxruntime-gpu>=1.15.0   # GPU support for InsightFace
# torch>=1.9.0              # Required for CUDA detection
# For MediaPipe GPU support, ensure proper OpenGL/EGL drivers are installed