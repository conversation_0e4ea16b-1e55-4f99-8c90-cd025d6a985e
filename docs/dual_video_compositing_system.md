# Dual Video Compositing System

## Overview

The Dual Video Compositing System implements a sophisticated approach for vertical video formatting when exactly two faces are detected. It creates two separate video outputs, each optimized for one face while maintaining all enhanced face positioning capabilities.

## Core Implementation

### Face Identification and Sorting
- **Horizontal Position Analysis**: Automatically sorts detected faces by x-coordinate
- **Leftmost Face**: Assigned to Video A (top half region)
- **Rightmost Face**: Assigned to Video B (bottom half region)
- **Consistent Assignment**: Maintains face assignments across frames for stability

### Dual Video Generation
- **Video A (Top Half)**: 720x640 region optimized for leftmost face
- **Video B (Bottom Half)**: 720x640 region optimized for rightmost face
- **Synchronized Outputs**: Frame-perfect temporal alignment
- **Enhanced Positioning**: Full predictive tracking for each face

## Technical Architecture

### Core Components

#### 1. DualVideoCompositor
Main compositor class handling dual video generation:

```python
class DualVideoCompositor:
    def __init__(self):
        self.target_width = 720
        self.target_height = 1280
        self.region_height = 640  # Half of target height
        
        # Separate face positioning engines for each track
        self.left_face_engine = FacePositioningEngine()
        self.right_face_engine = FacePositioningEngine()
```

#### 2. EnhancedDualFacePositioning
Integration layer extending existing face positioning:

```python
class EnhancedDualFacePositioning:
    def __init__(self):
        self.face_positioning_engine = FacePositioningEngine()
        self.dual_video_compositor = DualVideoCompositor()
        self.dual_face_threshold = 0.7  # 70% dual-face frames required
```

#### 3. Data Structures

**FaceTrack**: Individual face tracking data
```python
@dataclass
class FaceTrack:
    face_id: str  # "left" or "right"
    face_detections: List[FaceDetection]
    timestamps: List[float]
    layouts: List[FacePositionLayout]
    target_region: str  # "top_half" or "bottom_half"
```

**DualVideoOutput**: Complete dual video result
```python
@dataclass
class DualVideoOutput:
    video_a_path: str  # Top half video (leftmost face)
    video_b_path: str  # Bottom half video (rightmost face)
    metadata: Dict[str, Any]
    temporal_alignment: Dict[str, Any]
```

## Processing Workflow

### 1. Dual Face Detection
```python
# Automatic detection of dual-face scenarios
is_dual_scenario = DualVideoCompositor.is_dual_face_scenario(face_data_sequence)

# Requires 70% of frames to have exactly 2 faces
dual_face_ratio = dual_face_frames / total_frames
return dual_face_ratio >= 0.7
```

### 2. Face Track Separation
```python
# Sort faces by horizontal position
sorted_faces = sorted(faces, key=lambda f: f.center_x)

# Create separate tracks
left_track = FaceTrack(face_id="left", target_region="top_half", ...)
right_track = FaceTrack(face_id="right", target_region="bottom_half", ...)
```

### 3. Individual Layout Generation
```python
# Generate layouts for each face using separate engines
for face, timestamp in zip(left_track.face_detections, left_track.timestamps):
    layout = self.left_face_engine.calculate_face_positioning(
        faces=[face],
        target_width=720,
        target_height=640,  # Region height
        timestamp=timestamp
    )
    left_track.layouts.append(layout)
```

### 4. Video Generation
```python
# Generate separate videos for each face
video_a_path = self._generate_single_face_video(input_path, left_track, "top_half")
video_b_path = self._generate_single_face_video(input_path, right_track, "bottom_half")

# Optional: Create composite video
composite_path = self.create_composite_video(dual_output, output_path)
```

## Enhanced Features

### Predictive Tracking Per Face
- **Individual Engines**: Separate face positioning engines for each track
- **2-3 Second Lookahead**: Full predictive analysis for both faces
- **Velocity Calculation**: Independent movement tracking
- **Stability Scoring**: Per-face stability analysis

### Temporal Alignment
```python
temporal_alignment = {
    'frame_mapping': {
        'left_face_frames': len(left_track.timestamps),
        'right_face_frames': len(right_track.timestamps),
        'synchronized_frames': min(left_frames, right_frames)
    },
    'compositing_ready': True,
    'vertical_layout_data': {
        'top_region': {'face_id': 'left', 'y_offset': 0, 'height': 640},
        'bottom_region': {'face_id': 'right', 'y_offset': 640, 'height': 640}
    }
}
```

### Comprehensive Metadata
```python
metadata = {
    'video_a': {
        'face_id': 'left',
        'target_region': 'top_half',
        'region_dimensions': '720x640',
        'average_confidence': 0.92,
        'predictive_features': {
            'stability_analysis': {...},
            'velocity_analysis': {...},
            'segment_planning': {...}
        }
    },
    'video_b': {
        'face_id': 'right',
        'target_region': 'bottom_half',
        'region_dimensions': '720x640',
        'average_confidence': 0.88,
        'predictive_features': {...}
    }
}
```

## Usage Examples

### Basic Dual Video Processing
```python
from reframing.video.enhanced_dual_positioning import EnhancedDualFacePositioning

# Initialize enhanced positioning
enhanced_positioning = EnhancedDualFacePositioning()

# Process video with dual face detection
results = enhanced_positioning.process_video_with_enhanced_positioning(
    input_video_path="input.mp4",
    face_data_sequence=face_detections,
    output_dir="output/",
    base_filename="meeting_video"
)

# Access dual video outputs
if results['dual_output_generated']:
    video_a = results['outputs']['dual']['video_a_path']  # Left face video
    video_b = results['outputs']['dual']['video_b_path']  # Right face video
    composite = results['outputs']['dual']['composite_path']  # Combined video
```

### Configuration Options
```python
# Configure dual output behavior
enhanced_positioning.configure_dual_output(
    enable=True,           # Enable dual video generation
    preserve_single=True,  # Also generate traditional single output
    dual_threshold=0.8     # Require 80% dual-face frames
)

# Get processing recommendations
recommendations = enhanced_positioning.get_processing_recommendations(face_data)
print(f"Recommended approach: {recommendations['recommended_approach']}")
print(f"Expected benefits: {recommendations['expected_benefits']}")
```

### Direct Compositor Usage
```python
from reframing.video.dual_video_compositor import DualVideoCompositor

compositor = DualVideoCompositor()

# Check if scenario is suitable for dual output
if DualVideoCompositor.is_dual_face_scenario(face_data_sequence):
    # Generate dual videos
    dual_output = compositor.process_dual_face_video(
        input_video_path="input.mp4",
        face_data_sequence=face_data,
        output_dir="output/",
        base_filename="dual_video"
    )
    
    # Validate output
    validation = compositor.validate_dual_output(dual_output)
    print(f"Alignment ratio: {validation['temporal_alignment']['alignment_ratio']}")
```

## Integration with Existing System

### MediaPipe Compatibility
- **Full GPU Acceleration**: Maintains existing MediaPipe GPU support
- **CPU Fallback**: Graceful degradation when GPU unavailable
- **Pipeline Integration**: Seamless integration with existing video processing

### Enhanced Face Positioning
- **Backward Compatibility**: All existing functionality preserved
- **Automatic Detection**: Transparent dual-face scenario handling
- **Smooth Transitions**: Maintains transition quality for each face

### Video Test Generation
```python
# Enhanced video generator includes dual video scenarios
from tools.video_test_generator import VideoTestGenerator

generator = VideoTestGenerator()
scenarios = generator._create_test_scenarios()

# Find dual video compositing scenario
dual_scenario = next(s for s in scenarios if s.name == "dual_video_compositing")
video_path = generator._generate_scenario_video(dual_scenario)
```

## Output Specifications

### Video Files
- **Format**: MP4 with H.264 encoding
- **Resolution**: 720x1280 (vertical format)
- **Frame Rate**: 30 fps
- **Audio**: AAC encoding (from source)

### File Structure
```
output_directory/
├── video_name_left_face.mp4      # Video A (leftmost face)
├── video_name_right_face.mp4     # Video B (rightmost face)
├── video_name_composite.mp4      # Optional stacked composite
├── video_name_dual_metadata.json # Comprehensive metadata
└── video_name_single.mp4         # Traditional single output (optional)
```

### Metadata Schema
```json
{
  "dual_output": {
    "video_a_path": "path/to/left_face.mp4",
    "video_b_path": "path/to/right_face.mp4",
    "metadata": {
      "generation_info": {...},
      "video_a": {...},
      "video_b": {...},
      "synchronization": {...}
    },
    "temporal_alignment": {...}
  }
}
```

## Performance Characteristics

### Processing Efficiency
- **Parallel Processing**: Independent face track processing
- **Memory Optimization**: Efficient face history management
- **Real-time Capability**: Maintains 30fps processing speed

### Quality Metrics
- **Face Confidence**: Average confidence scores per track
- **Stability Analysis**: Predictive tracking stability
- **Temporal Alignment**: Frame synchronization accuracy
- **Validation Results**: Comprehensive quality assessment

## Benefits and Use Cases

### Optimal Face Framing
- **Individual Optimization**: Each face gets optimal framing within its region
- **Elimination of Overlap**: No face overlap issues in dual scenarios
- **Consistent Positioning**: Stable face positioning across frames

### Enhanced Video Calls
- **Meeting Recordings**: Optimal framing for two-person meetings
- **Interview Content**: Professional framing for interviewer/interviewee
- **Collaborative Sessions**: Clear visibility for both participants

### Future Compositing
- **Synchronized Outputs**: Perfect temporal alignment for compositing
- **Flexible Layouts**: Easy recombination into different layouts
- **Metadata Rich**: Comprehensive data for advanced post-processing

## Validation and Testing

### Comprehensive Test Suite
- **Unit Tests**: All components thoroughly tested
- **Integration Tests**: End-to-end workflow validation
- **Performance Tests**: Speed and quality benchmarks

### Visual Validation
- **Test Video Generation**: Synthetic dual-face scenarios
- **Quality Assessment**: Visual inspection of outputs
- **Comparison Analysis**: Side-by-side validation

The dual video compositing system provides a sophisticated solution for two-face scenarios, ensuring optimal framing and enhanced user experience while maintaining all the advanced features of the enhanced face positioning system.
