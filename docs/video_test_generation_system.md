# Video Test Generation System

## Overview

The Video Test Generation System creates comprehensive synthetic test videos to validate and demonstrate the enhanced face positioning system. It generates realistic face detection scenarios with visual overlays, performance metrics, and side-by-side comparisons.

## Features

### 🎬 **Synthetic Video Generation**
- **MP4 format** with H.264 encoding at 1920x1080 resolution, 30 fps
- **Realistic face movements** with multiple movement patterns (linear, circular, stationary, bounce)
- **Configurable face parameters** including size (150-300px), confidence scores (0.8-0.95), and timing
- **Visual overlays** showing face detection boxes, layout regions, crop areas, and system metrics

### 🎯 **Comprehensive Test Scenarios**

#### 1. Layout Transition Demo (30 seconds)
- **Purpose**: Demonstrates smooth transitions between all layout types
- **Sequence**: Single → Dual → Triple → Quad face layouts
- **Features**: Layout transitions, smooth transitions, face assignments

#### 2. Predictive Tracking Demo (20 seconds)
- **Purpose**: Single face moving to demonstrate 2-3 second lookahead
- **Movement**: Linear motion across the entire frame
- **Features**: Predictive tracking, velocity calculation, segment planning

#### 3. Stability Test (15 seconds)
- **Purpose**: Faces entering/exiting to test assignment consistency
- **Pattern**: Stable face + entering/exiting faces + brief appearances
- **Features**: Assignment consistency, face stability, layout stability

#### 4. Real-world Simulation (45 seconds)
- **Purpose**: Mimics typical video call scenarios with natural movements
- **Pattern**: Main speaker + participants joining/leaving + complex interactions
- **Features**: Real-world patterns, natural movements, complex scenarios

### 📊 **Validation Features**

#### Visual Indicators
- **Face Detection Boxes**: Green rectangles showing detected face regions
- **Layout Regions**: Blue rectangles showing assigned layout zones
- **Crop Area**: Red rectangle showing the final crop region
- **Assignment Lines**: Cyan lines connecting faces to their assigned regions
- **Confidence Scores**: Numerical confidence values for each detection

#### Overlay Information
- **Scenario Details**: Current scenario name and timestamp
- **Face Count**: Number of detected faces
- **Layout Type**: Current layout (single_centered, dual_vertical, etc.)
- **Confidence Score**: Overall layout confidence
- **Crop Position**: Current crop coordinates
- **Predictive Data**: Average stability scores and segment information

#### Performance Metrics
- **Layout Transitions**: Count of layout type changes
- **Average Confidence**: Mean confidence across all frames
- **Stability Scores**: Face tracking stability metrics
- **Face Count Distribution**: Histogram of face counts over time

## Usage

### Command Line Interface

```bash
# Generate all test scenarios
python tools/video_test_generator.py

# Generate specific scenario
python tools/video_test_generator.py --scenario predictive_tracking

# Custom output directory
python tools/video_test_generator.py --output-dir my_test_videos

# Skip comparison videos
python tools/video_test_generator.py --no-comparison

# Verbose logging
python tools/video_test_generator.py --verbose
```

### Programmatic Usage

```python
from tools.video_test_generator import VideoTestGenerator

# Create generator
generator = VideoTestGenerator(output_dir="test_videos")

# Generate all scenarios
generated_videos = generator.generate_all_test_videos()

# Generate performance report
report_path = generator.generate_performance_report(generated_videos)
```

### Custom Scenario Creation

```python
from tools.video_test_generator import TestFace, VideoScenario

# Define custom faces
custom_faces = [
    TestFace(
        id=1,
        start_time=0.0,
        end_time=10.0,
        start_pos=(400, 300),
        end_pos=(1200, 700),
        size=(200, 200),
        confidence=0.92,
        movement_type='linear',
        color=(255, 100, 100)
    )
]

# Create custom scenario
scenario = VideoScenario(
    name="custom_test",
    duration=10.0,
    description="Custom test scenario",
    faces=custom_faces,
    target_features=["custom_feature"]
)

# Generate video
video_path = generator._generate_scenario_video(scenario)
```

## Output Files

### Generated Videos
- **`{scenario_name}.mp4`**: Original test video with overlays
- **`{scenario_name}_comparison.mp4`**: Side-by-side comparison (original vs cropped)

### Data Files
- **`{scenario_name}_faces.json`**: Frame-by-frame face detection and layout data
- **`performance_report.json`**: Comprehensive performance analysis

### Directory Structure
```
test_videos/
├── layout_transitions.mp4
├── layout_transitions_comparison.mp4
├── layout_transitions_faces.json
├── predictive_tracking.mp4
├── predictive_tracking_comparison.mp4
├── predictive_tracking_faces.json
├── stability_test.mp4
├── stability_test_comparison.mp4
├── stability_test_faces.json
├── realworld_simulation.mp4
├── realworld_simulation_comparison.mp4
├── realworld_simulation_faces.json
└── performance_report.json
```

## Validation Workflow

### 1. Generate Test Videos
```bash
python tools/video_test_generator.py --scenario all
```

### 2. Visual Validation
- **Review original videos** to observe face detection and layout behavior
- **Check comparison videos** to see original vs. cropped output quality
- **Verify smooth transitions** between layout changes
- **Confirm face assignments** remain consistent across frames

### 3. Data Analysis
- **Examine JSON files** for frame-by-frame metrics
- **Analyze confidence scores** and stability measurements
- **Review predictive tracking** accuracy and segment planning
- **Check layout transition** timing and frequency

### 4. Performance Evaluation
- **Review performance report** for quantitative metrics
- **Analyze face count distributions** across scenarios
- **Evaluate average confidence** and stability scores
- **Check processing performance** and frame rates

## Technical Specifications

### Video Parameters
- **Resolution**: 1920x1080 (input), 720x1280 (target crop)
- **Frame Rate**: 30 fps for smooth motion demonstration
- **Encoding**: H.264 MP4 format for broad compatibility
- **Duration**: 15-45 seconds per scenario (optimized for validation)

### Face Detection Simulation
- **Realistic Bounding Boxes**: Proper aspect ratios and positioning
- **Confidence Variation**: Slight random variation (±0.02) to simulate real detection
- **Position Noise**: Small random offsets (±2 pixels) for realistic jitter
- **Size Range**: 150-300 pixels appropriate for 1920x1080 resolution

### Movement Patterns
- **Linear**: Straight-line movement between start and end positions
- **Circular**: Circular motion around center point with configurable radius
- **Stationary**: Fixed position with minimal noise
- **Bounce**: Sinusoidal back-and-forth movement

## Integration with Face Positioning System

The video generator integrates seamlessly with the enhanced face positioning system:

```python
# Process each frame through the face positioning engine
layout = self.face_engine.calculate_face_positioning(
    faces=face_detections,
    frame_width=self.width,
    frame_height=self.height,
    target_width=self.target_width,
    target_height=self.target_height,
    timestamp=timestamp
)

# Capture all system outputs
- Layout type and confidence
- Predictive data and stability scores
- Segment plans and transition information
- Debug information and performance metrics
```

## Troubleshooting

### Common Issues

**Video Generation Fails**
- Check OpenCV installation: `pip install opencv-python`
- Verify output directory permissions
- Ensure sufficient disk space

**Comparison Video Errors**
- Check crop coordinates are within frame bounds
- Verify target dimensions match expected values
- Review frame size consistency

**Performance Issues**
- Reduce video duration for faster generation
- Use `--no-comparison` to skip comparison videos
- Generate single scenarios instead of all at once

### Dependencies
```bash
pip install opencv-python numpy pathlib
```

## Future Enhancements

### Planned Features
- **Real video integration**: Support for processing actual video files
- **Advanced movement patterns**: Bezier curves, acceleration/deceleration
- **Multi-person scenarios**: Complex interactions between multiple faces
- **Performance benchmarking**: Automated performance comparison tools
- **Export formats**: Additional video formats and quality settings
