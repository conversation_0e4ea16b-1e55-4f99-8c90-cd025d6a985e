#!/usr/bin/env python3
"""
Dual Video Compositor for Enhanced Face Positioning System

This module implements dual-video compositing for vertical video formatting when exactly
two faces are detected. It creates two separate video outputs, each optimized for one face
while maintaining all enhanced face positioning capabilities.

Features:
- Face identification by horizontal position (leftmost/rightmost)
- Dual video generation with synchronized outputs
- Enhanced face positioning with predictive tracking for each face
- Optimal framing within 720x640 regions
- Temporal alignment for future compositing
"""

import logging
import os
import subprocess
import tempfile
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

try:
    from ..models.data_classes import FaceDetection
    from .face_positioning import FacePositioningEngine, FaceLayoutType, FacePositionLayout
except ImportError:
    # Fallback for testing
    from dataclasses import dataclass as _dataclass

    @_dataclass
    class FaceDetection:
        x: int
        y: int
        width: int
        height: int
        confidence: float
        center_x: float
        center_y: float


@dataclass
class DualVideoOutput:
    """Container for dual video output information"""
    video_a_path: str  # Top half video (leftmost face)
    video_b_path: str  # Bottom half video (rightmost face)
    metadata: Dict[str, Any]
    temporal_alignment: Dict[str, Any]


@dataclass
class FaceTrack:
    """Individual face track for dual video generation"""
    face_id: str  # "left" or "right"
    face_detections: List[FaceDetection]
    timestamps: List[float]
    layouts: List[FacePositionLayout]
    target_region: str  # "top_half" or "bottom_half"


class DualVideoCompositor:
    """
    Advanced dual video compositor for two-face scenarios
    
    Creates two separate video outputs when exactly two faces are detected,
    with each video optimized for one face using enhanced face positioning.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Video specifications
        self.target_width = 720
        self.target_height = 1280
        self.region_height = 640  # Half of target height
        
        # Face positioning engines for each track
        self.left_face_engine = FacePositioningEngine()
        self.right_face_engine = FacePositioningEngine()
        
        # Configuration
        self.fps = 30
        self.video_codec = 'libx264'
        self.audio_codec = 'aac'

    def process_dual_face_video(self, input_video_path: str, face_data_sequence: List[Dict[str, Any]],
                               output_dir: str, base_filename: str) -> DualVideoOutput:
        """
        Process video with dual face detection to create two separate outputs
        
        Args:
            input_video_path: Path to source video
            face_data_sequence: Sequence of face detection data with timestamps
            output_dir: Output directory for generated videos
            base_filename: Base filename for output videos
            
        Returns:
            DualVideoOutput containing paths and metadata
        """
        self.logger.info("🎬 Starting dual video compositing for two-face scenario")
        
        # Validate input
        if not self._validate_dual_face_data(face_data_sequence):
            raise ValueError("Face data sequence does not contain consistent dual face detection")
        
        # Separate face tracks
        left_track, right_track = self._separate_face_tracks(face_data_sequence)
        
        # Generate individual face layouts for each track
        self._generate_individual_layouts(left_track, right_track)
        
        # Create output paths
        video_a_path = os.path.join(output_dir, f"{base_filename}_left_face.mp4")
        video_b_path = os.path.join(output_dir, f"{base_filename}_right_face.mp4")
        
        # Generate video A (leftmost face in top region)
        self.logger.info("📹 Generating Video A (leftmost face - top region)")
        success_a = self._generate_single_face_video(
            input_video_path, video_a_path, left_track, "top_half"
        )
        
        # Generate video B (rightmost face in bottom region)
        self.logger.info("📹 Generating Video B (rightmost face - bottom region)")
        success_b = self._generate_single_face_video(
            input_video_path, video_b_path, right_track, "bottom_half"
        )
        
        if not (success_a and success_b):
            raise RuntimeError("Failed to generate one or both dual videos")
        
        # Create metadata
        metadata = self._create_dual_video_metadata(left_track, right_track, face_data_sequence)
        
        # Create temporal alignment data
        temporal_alignment = self._create_temporal_alignment_data(left_track, right_track)
        
        self.logger.info("✅ Dual video compositing completed successfully")
        
        return DualVideoOutput(
            video_a_path=video_a_path,
            video_b_path=video_b_path,
            metadata=metadata,
            temporal_alignment=temporal_alignment
        )

    def _validate_dual_face_data(self, face_data_sequence: List[Dict[str, Any]]) -> bool:
        """Validate that face data contains consistent dual face detection"""
        if not face_data_sequence:
            return False
        
        # Check that most frames have exactly 2 faces
        dual_face_frames = 0
        for frame_data in face_data_sequence:
            if len(frame_data.get('faces', [])) == 2:
                dual_face_frames += 1
        
        # Require at least 80% of frames to have dual faces
        dual_face_ratio = dual_face_frames / len(face_data_sequence)
        return dual_face_ratio >= 0.8

    def _separate_face_tracks(self, face_data_sequence: List[Dict[str, Any]]) -> Tuple[FaceTrack, FaceTrack]:
        """Separate face detections into left and right tracks"""
        left_detections = []
        right_detections = []
        left_timestamps = []
        right_timestamps = []
        
        for frame_data in face_data_sequence:
            faces = frame_data.get('faces', [])
            timestamp = frame_data.get('timestamp', 0.0)
            
            if len(faces) == 2:
                # Sort faces by x-coordinate (leftmost first)
                sorted_faces = sorted(faces, key=lambda f: f.get('center_x', f.get('x', 0)))
                
                # Convert to FaceDetection objects
                left_face = self._dict_to_face_detection(sorted_faces[0])
                right_face = self._dict_to_face_detection(sorted_faces[1])
                
                left_detections.append(left_face)
                right_detections.append(right_face)
                left_timestamps.append(timestamp)
                right_timestamps.append(timestamp)
        
        left_track = FaceTrack(
            face_id="left",
            face_detections=left_detections,
            timestamps=left_timestamps,
            layouts=[],
            target_region="top_half"
        )
        
        right_track = FaceTrack(
            face_id="right",
            face_detections=right_detections,
            timestamps=right_timestamps,
            layouts=[],
            target_region="bottom_half"
        )
        
        return left_track, right_track

    def _dict_to_face_detection(self, face_dict: Dict[str, Any]) -> FaceDetection:
        """Convert face dictionary to FaceDetection object"""
        return FaceDetection(
            x=face_dict.get('x', 0),
            y=face_dict.get('y', 0),
            width=face_dict.get('width', 100),
            height=face_dict.get('height', 100),
            confidence=face_dict.get('confidence', 0.8),
            center_x=face_dict.get('center_x', face_dict.get('x', 0) + face_dict.get('width', 100) / 2),
            center_y=face_dict.get('center_y', face_dict.get('y', 0) + face_dict.get('height', 100) / 2)
        )

    def _generate_individual_layouts(self, left_track: FaceTrack, right_track: FaceTrack):
        """Generate individual face positioning layouts for each track"""
        self.logger.info("🎯 Generating individual layouts for each face track")
        
        # Process left face track
        for i, (face, timestamp) in enumerate(zip(left_track.face_detections, left_track.timestamps)):
            layout = self.left_face_engine.calculate_face_positioning(
                faces=[face],
                frame_width=1920,  # Assume standard input resolution
                frame_height=1080,
                target_width=self.target_width,
                target_height=self.region_height,  # Use region height for individual face
                timestamp=timestamp
            )
            left_track.layouts.append(layout)
        
        # Process right face track
        for i, (face, timestamp) in enumerate(zip(right_track.face_detections, right_track.timestamps)):
            layout = self.right_face_engine.calculate_face_positioning(
                faces=[face],
                frame_width=1920,  # Assume standard input resolution
                frame_height=1080,
                target_width=self.target_width,
                target_height=self.region_height,  # Use region height for individual face
                timestamp=timestamp
            )
            right_track.layouts.append(layout)
        
        self.logger.info(f"✅ Generated {len(left_track.layouts)} layouts for left track")
        self.logger.info(f"✅ Generated {len(right_track.layouts)} layouts for right track")

    def _generate_single_face_video(self, input_path: str, output_path: str,
                                   face_track: FaceTrack, target_region: str) -> bool:
        """
        Generate a single video focused on one face

        Args:
            input_path: Source video path
            output_path: Output video path
            face_track: Face track data
            target_region: Target region ("top_half" or "bottom_half")

        Returns:
            True if successful
        """
        try:
            # Create crop filter for this face track
            crop_filter = self._create_face_track_crop_filter(face_track)

            # Determine vertical offset based on target region
            if target_region == "top_half":
                y_offset = 0
            else:  # bottom_half
                y_offset = self.region_height

            # Build FFmpeg command
            cmd = [
                'ffmpeg', '-y',
                '-i', input_path,
                '-vf', f"{crop_filter},scale={self.target_width}:{self.target_height}",
                '-c:v', self.video_codec,
                '-c:a', self.audio_codec,
                '-r', str(self.fps),
                output_path
            ]

            self.logger.info(f"🎬 Generating {face_track.face_id} face video: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"✅ Successfully generated {face_track.face_id} face video: {output_path}")
                return True
            else:
                self.logger.error(f"❌ Failed to generate {face_track.face_id} face video: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error generating {face_track.face_id} face video: {str(e)}")
            return False

    def _create_face_track_crop_filter(self, face_track: FaceTrack) -> str:
        """Create FFmpeg crop filter for a face track"""
        if not face_track.layouts:
            # Fallback to center crop
            return f"crop={self.target_width}:{self.region_height}:600:200"

        # Create keyframe-based crop filter
        keyframes = []

        for i, layout in enumerate(face_track.layouts):
            timestamp = face_track.timestamps[i]

            # Use layout crop position but adjust for region height
            crop_x = layout.crop_x
            crop_y = layout.crop_y

            # Ensure crop fits within region
            crop_x = max(0, min(1920 - self.target_width, crop_x))
            crop_y = max(0, min(1080 - self.region_height, crop_y))

            keyframes.append(f"crop={self.target_width}:{self.region_height}:{crop_x}:{crop_y}")

        # For now, use the first keyframe (can be enhanced with temporal interpolation)
        return keyframes[0] if keyframes else f"crop={self.target_width}:{self.region_height}:600:200"

    def _create_dual_video_metadata(self, left_track: FaceTrack, right_track: FaceTrack,
                                   face_data_sequence: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create metadata for dual video output"""
        return {
            'generation_info': {
                'compositor_version': '1.0',
                'face_positioning_engine': 'enhanced_predictive',
                'total_frames': len(face_data_sequence),
                'dual_face_frames': len(left_track.face_detections)
            },
            'video_a': {
                'face_id': 'left',
                'target_region': 'top_half',
                'region_dimensions': f"{self.target_width}x{self.region_height}",
                'face_count': len(left_track.face_detections),
                'average_confidence': self._calculate_average_confidence(left_track),
                'predictive_features': self._extract_predictive_features(left_track)
            },
            'video_b': {
                'face_id': 'right',
                'target_region': 'bottom_half',
                'region_dimensions': f"{self.target_width}x{self.region_height}",
                'face_count': len(right_track.face_detections),
                'average_confidence': self._calculate_average_confidence(right_track),
                'predictive_features': self._extract_predictive_features(right_track)
            },
            'synchronization': {
                'temporal_alignment': 'frame_perfect',
                'timestamp_range': {
                    'start': min(left_track.timestamps + right_track.timestamps),
                    'end': max(left_track.timestamps + right_track.timestamps)
                }
            }
        }

    def _create_temporal_alignment_data(self, left_track: FaceTrack, right_track: FaceTrack) -> Dict[str, Any]:
        """Create temporal alignment data for future compositing"""
        return {
            'frame_mapping': {
                'left_face_frames': len(left_track.timestamps),
                'right_face_frames': len(right_track.timestamps),
                'synchronized_frames': min(len(left_track.timestamps), len(right_track.timestamps))
            },
            'timestamp_alignment': [
                {
                    'frame_index': i,
                    'left_timestamp': left_track.timestamps[i] if i < len(left_track.timestamps) else None,
                    'right_timestamp': right_track.timestamps[i] if i < len(right_track.timestamps) else None,
                    'synchronized': (i < len(left_track.timestamps) and i < len(right_track.timestamps))
                }
                for i in range(max(len(left_track.timestamps), len(right_track.timestamps)))
            ],
            'compositing_ready': True,
            'vertical_layout_data': {
                'top_region': {'face_id': 'left', 'y_offset': 0, 'height': self.region_height},
                'bottom_region': {'face_id': 'right', 'y_offset': self.region_height, 'height': self.region_height}
            }
        }

    def _calculate_average_confidence(self, face_track: FaceTrack) -> float:
        """Calculate average confidence for a face track"""
        if not face_track.face_detections:
            return 0.0

        total_confidence = sum(face.confidence for face in face_track.face_detections)
        return total_confidence / len(face_track.face_detections)

    def _extract_predictive_features(self, face_track: FaceTrack) -> Dict[str, Any]:
        """Extract predictive features from face track layouts"""
        if not face_track.layouts:
            return {}

        # Collect predictive data from layouts
        stability_scores = []
        velocity_data = []
        segment_plans = []

        for layout in face_track.layouts:
            if layout.predictive_data:
                for pred_data in layout.predictive_data:
                    stability_scores.append(pred_data.stability_score)
                    velocity_data.append(pred_data.velocity)

            if layout.segment_plan:
                segment_plans.append({
                    'start_time': layout.segment_plan.start_time,
                    'end_time': layout.segment_plan.end_time,
                    'stability_score': layout.segment_plan.stability_score,
                    'confidence': layout.segment_plan.confidence
                })

        return {
            'stability_analysis': {
                'average_stability': sum(stability_scores) / len(stability_scores) if stability_scores else 0.0,
                'stability_range': [min(stability_scores), max(stability_scores)] if stability_scores else [0.0, 0.0],
                'total_predictions': len(stability_scores)
            },
            'velocity_analysis': {
                'average_velocity': {
                    'x': sum(v[0] for v in velocity_data) / len(velocity_data) if velocity_data else 0.0,
                    'y': sum(v[1] for v in velocity_data) / len(velocity_data) if velocity_data else 0.0
                },
                'movement_detected': len([v for v in velocity_data if abs(v[0]) > 1 or abs(v[1]) > 1]) > 0
            },
            'segment_planning': {
                'total_segments': len(segment_plans),
                'segments': segment_plans[:5]  # Include first 5 segments for metadata
            }
        }

    def create_composite_video(self, dual_output: DualVideoOutput, output_path: str) -> bool:
        """
        Create a composite video by stacking the two individual videos vertically

        Args:
            dual_output: DualVideoOutput containing the two video paths
            output_path: Path for the composite output video

        Returns:
            True if successful
        """
        try:
            # Create FFmpeg command to stack videos vertically
            cmd = [
                'ffmpeg', '-y',
                '-i', dual_output.video_a_path,
                '-i', dual_output.video_b_path,
                '-filter_complex', '[0:v][1:v]vstack=inputs=2[v]',
                '-map', '[v]',
                '-map', '0:a',  # Use audio from first video
                '-c:v', self.video_codec,
                '-c:a', self.audio_codec,
                output_path
            ]

            self.logger.info(f"🎬 Creating composite video: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"✅ Successfully created composite video: {output_path}")
                return True
            else:
                self.logger.error(f"❌ Failed to create composite video: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error creating composite video: {str(e)}")
            return False

    def validate_dual_output(self, dual_output: DualVideoOutput) -> Dict[str, Any]:
        """
        Validate the dual video output and provide quality metrics

        Args:
            dual_output: DualVideoOutput to validate

        Returns:
            Validation results and quality metrics
        """
        validation_results = {
            'files_exist': {
                'video_a': os.path.exists(dual_output.video_a_path),
                'video_b': os.path.exists(dual_output.video_b_path)
            },
            'file_sizes': {},
            'temporal_alignment': {},
            'quality_metrics': {}
        }

        # Check file sizes
        if validation_results['files_exist']['video_a']:
            validation_results['file_sizes']['video_a'] = os.path.getsize(dual_output.video_a_path)

        if validation_results['files_exist']['video_b']:
            validation_results['file_sizes']['video_b'] = os.path.getsize(dual_output.video_b_path)

        # Validate temporal alignment
        alignment_data = dual_output.temporal_alignment
        validation_results['temporal_alignment'] = {
            'synchronized_frames': alignment_data['frame_mapping']['synchronized_frames'],
            'total_frames': max(
                alignment_data['frame_mapping']['left_face_frames'],
                alignment_data['frame_mapping']['right_face_frames']
            ),
            'alignment_ratio': (
                alignment_data['frame_mapping']['synchronized_frames'] /
                max(alignment_data['frame_mapping']['left_face_frames'],
                    alignment_data['frame_mapping']['right_face_frames'])
                if max(alignment_data['frame_mapping']['left_face_frames'],
                       alignment_data['frame_mapping']['right_face_frames']) > 0 else 0
            )
        }

        # Extract quality metrics from metadata
        metadata = dual_output.metadata
        validation_results['quality_metrics'] = {
            'video_a_confidence': metadata['video_a']['average_confidence'],
            'video_b_confidence': metadata['video_b']['average_confidence'],
            'video_a_stability': metadata['video_a']['predictive_features'].get(
                'stability_analysis', {}
            ).get('average_stability', 0.0),
            'video_b_stability': metadata['video_b']['predictive_features'].get(
                'stability_analysis', {}
            ).get('average_stability', 0.0)
        }

        return validation_results

    @staticmethod
    def is_dual_face_scenario(face_data_sequence: List[Dict[str, Any]]) -> bool:
        """
        Check if the face data sequence represents a dual face scenario

        Args:
            face_data_sequence: Sequence of face detection data

        Returns:
            True if this is a dual face scenario
        """
        if not face_data_sequence:
            return False

        # Count frames with exactly 2 faces
        dual_face_frames = sum(
            1 for frame_data in face_data_sequence
            if len(frame_data.get('faces', [])) == 2
        )

        # Require at least 70% of frames to have dual faces
        dual_face_ratio = dual_face_frames / len(face_data_sequence)
        return dual_face_ratio >= 0.7

    def get_debug_info(self) -> Dict[str, Any]:
        """Get debug information about the dual video compositor"""
        return {
            'compositor_config': {
                'target_width': self.target_width,
                'target_height': self.target_height,
                'region_height': self.region_height,
                'fps': self.fps,
                'video_codec': self.video_codec,
                'audio_codec': self.audio_codec
            },
            'face_engines': {
                'left_engine_history': len(self.left_face_engine.face_history),
                'right_engine_history': len(self.right_face_engine.face_history),
                'left_engine_segments': len(self.left_face_engine.segment_plans),
                'right_engine_segments': len(self.right_face_engine.segment_plans)
            }
        }
