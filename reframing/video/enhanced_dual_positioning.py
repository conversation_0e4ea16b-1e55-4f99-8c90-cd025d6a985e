#!/usr/bin/env python3
"""
Enhanced Dual Face Positioning Integration

This module extends the existing face positioning system to support dual-video compositing
when exactly two faces are detected. It integrates the DualVideoCompositor with the
enhanced face positioning engine while maintaining all existing functionality.

Features:
- Automatic detection of dual-face scenarios
- Seamless integration with existing MediaPipe pipeline
- Enhanced face positioning with predictive tracking for dual outputs
- Backward compatibility with single video output
- Comprehensive metadata and validation
"""

import logging
import os
import json
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

try:
    from .face_positioning import FacePositioningEngine, FaceLayoutType
    from .dual_video_compositor import DualVideoCompositor, DualVideoOutput
    from ..models.data_classes import FaceDetection
except ImportError:
    # Fallback for testing
    pass


class EnhancedDualFacePositioning:
    """
    Enhanced face positioning system with dual-video compositing support
    
    Extends the existing face positioning engine to automatically detect dual-face
    scenarios and generate optimized dual video outputs while maintaining all
    existing functionality for other face count scenarios.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Core components
        self.face_positioning_engine = FacePositioningEngine()
        self.dual_video_compositor = DualVideoCompositor()
        
        # Configuration
        self.dual_face_threshold = 0.7  # Minimum ratio of dual-face frames to trigger dual output
        self.enable_dual_output = True
        self.preserve_single_output = True  # Also generate traditional single output
        
        # State tracking
        self.last_face_count = 0
        self.dual_face_detection_active = False

    def process_video_with_enhanced_positioning(self, input_video_path: str, 
                                              face_data_sequence: List[Dict[str, Any]],
                                              output_dir: str, base_filename: str,
                                              generate_dual_output: Optional[bool] = None) -> Dict[str, Any]:
        """
        Process video with enhanced face positioning, automatically handling dual-face scenarios
        
        Args:
            input_video_path: Path to source video
            face_data_sequence: Sequence of face detection data with timestamps
            output_dir: Output directory for generated videos
            base_filename: Base filename for output videos
            generate_dual_output: Override automatic dual-face detection
            
        Returns:
            Processing results with paths and metadata
        """
        self.logger.info("🎯 Starting enhanced dual face positioning processing")
        
        # Analyze face data to determine processing approach
        analysis_results = self._analyze_face_data_sequence(face_data_sequence)
        
        # Determine if dual output should be generated
        should_generate_dual = (
            generate_dual_output if generate_dual_output is not None
            else (self.enable_dual_output and analysis_results['is_dual_face_scenario'])
        )
        
        results = {
            'analysis': analysis_results,
            'dual_output_generated': should_generate_dual,
            'single_output_generated': False,
            'outputs': {},
            'metadata': {},
            'validation': {}
        }
        
        # Generate dual video output if applicable
        if should_generate_dual:
            self.logger.info("📹 Generating dual video output for two-face scenario")
            dual_output = self._generate_dual_video_output(
                input_video_path, face_data_sequence, output_dir, base_filename
            )
            
            results['outputs']['dual'] = {
                'video_a_path': dual_output.video_a_path,
                'video_b_path': dual_output.video_b_path,
                'composite_path': None
            }
            results['metadata']['dual'] = dual_output.metadata
            results['validation']['dual'] = self.dual_video_compositor.validate_dual_output(dual_output)
            
            # Optionally create composite video
            composite_path = os.path.join(output_dir, f"{base_filename}_composite.mp4")
            if self.dual_video_compositor.create_composite_video(dual_output, composite_path):
                results['outputs']['dual']['composite_path'] = composite_path
        
        # Generate traditional single output if requested or as fallback
        if self.preserve_single_output or not should_generate_dual:
            self.logger.info("📹 Generating traditional single video output")
            single_output = self._generate_single_video_output(
                input_video_path, face_data_sequence, output_dir, base_filename
            )
            
            results['single_output_generated'] = single_output is not None
            if single_output:
                results['outputs']['single'] = single_output
        
        self.logger.info("✅ Enhanced dual face positioning processing completed")
        return results

    def _analyze_face_data_sequence(self, face_data_sequence: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze face data sequence to determine processing approach"""
        if not face_data_sequence:
            return {
                'is_dual_face_scenario': False,
                'total_frames': 0,
                'face_count_distribution': {},
                'dual_face_ratio': 0.0,
                'recommendation': 'single_output'
            }
        
        # Count face occurrences
        face_count_distribution = {}
        for frame_data in face_data_sequence:
            face_count = len(frame_data.get('faces', []))
            face_count_distribution[face_count] = face_count_distribution.get(face_count, 0) + 1
        
        # Calculate dual face ratio
        dual_face_frames = face_count_distribution.get(2, 0)
        total_frames = len(face_data_sequence)
        dual_face_ratio = dual_face_frames / total_frames if total_frames > 0 else 0.0
        
        # Determine if this is a dual face scenario
        is_dual_face_scenario = dual_face_ratio >= self.dual_face_threshold
        
        # Generate recommendation
        if is_dual_face_scenario:
            recommendation = 'dual_output'
        elif face_count_distribution.get(1, 0) / total_frames > 0.8:
            recommendation = 'single_output'
        else:
            recommendation = 'mixed_scenario'
        
        return {
            'is_dual_face_scenario': is_dual_face_scenario,
            'total_frames': total_frames,
            'face_count_distribution': face_count_distribution,
            'dual_face_ratio': dual_face_ratio,
            'recommendation': recommendation,
            'dominant_face_count': max(face_count_distribution.keys(), 
                                     key=lambda k: face_count_distribution[k]) if face_count_distribution else 0
        }

    def _generate_dual_video_output(self, input_video_path: str, face_data_sequence: List[Dict[str, Any]],
                                   output_dir: str, base_filename: str) -> DualVideoOutput:
        """Generate dual video output using the dual video compositor"""
        try:
            dual_output = self.dual_video_compositor.process_dual_face_video(
                input_video_path=input_video_path,
                face_data_sequence=face_data_sequence,
                output_dir=output_dir,
                base_filename=base_filename
            )
            
            # Save dual video metadata
            metadata_path = os.path.join(output_dir, f"{base_filename}_dual_metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump({
                    'dual_output': {
                        'video_a_path': dual_output.video_a_path,
                        'video_b_path': dual_output.video_b_path,
                        'metadata': dual_output.metadata,
                        'temporal_alignment': dual_output.temporal_alignment
                    }
                }, f, indent=2)
            
            self.logger.info(f"💾 Saved dual video metadata: {metadata_path}")
            return dual_output
            
        except Exception as e:
            self.logger.error(f"❌ Failed to generate dual video output: {str(e)}")
            raise

    def _generate_single_video_output(self, input_video_path: str, face_data_sequence: List[Dict[str, Any]],
                                     output_dir: str, base_filename: str) -> Optional[Dict[str, Any]]:
        """Generate traditional single video output using enhanced face positioning"""
        try:
            # Process face data through enhanced positioning engine
            layouts = []
            for frame_data in face_data_sequence:
                faces = [self._dict_to_face_detection(f) for f in frame_data.get('faces', [])]
                timestamp = frame_data.get('timestamp', 0.0)
                
                if faces:
                    layout = self.face_positioning_engine.calculate_face_positioning(
                        faces=faces,
                        frame_width=1920,  # Assume standard input resolution
                        frame_height=1080,
                        target_width=720,
                        target_height=1280,
                        timestamp=timestamp
                    )
                    layouts.append(layout)
            
            # Generate single video output (this would integrate with existing video generation)
            single_output_path = os.path.join(output_dir, f"{base_filename}_single.mp4")
            
            # Create metadata for single output
            single_metadata = {
                'output_type': 'single_video',
                'total_layouts': len(layouts),
                'layout_types': [layout.layout_type.value for layout in layouts],
                'average_confidence': sum(layout.confidence for layout in layouts) / len(layouts) if layouts else 0.0,
                'enhanced_features': {
                    'predictive_tracking': True,
                    'smooth_transitions': True,
                    'segment_planning': True
                }
            }
            
            # Save single video metadata
            metadata_path = os.path.join(output_dir, f"{base_filename}_single_metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(single_metadata, f, indent=2)
            
            return {
                'video_path': single_output_path,
                'metadata_path': metadata_path,
                'metadata': single_metadata
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to generate single video output: {str(e)}")
            return None

    def _dict_to_face_detection(self, face_dict: Dict[str, Any]) -> FaceDetection:
        """Convert face dictionary to FaceDetection object"""
        return FaceDetection(
            x=face_dict.get('x', 0),
            y=face_dict.get('y', 0),
            width=face_dict.get('width', 100),
            height=face_dict.get('height', 100),
            confidence=face_dict.get('confidence', 0.8),
            center_x=face_dict.get('center_x', face_dict.get('x', 0) + face_dict.get('width', 100) / 2),
            center_y=face_dict.get('center_y', face_dict.get('y', 0) + face_dict.get('height', 100) / 2)
        )

    def configure_dual_output(self, enable: bool = True, preserve_single: bool = True,
                            dual_threshold: float = 0.7):
        """
        Configure dual output settings
        
        Args:
            enable: Enable dual video output generation
            preserve_single: Also generate traditional single output
            dual_threshold: Minimum ratio of dual-face frames to trigger dual output
        """
        self.enable_dual_output = enable
        self.preserve_single_output = preserve_single
        self.dual_face_threshold = dual_threshold
        
        self.logger.info(f"🔧 Configured dual output: enable={enable}, preserve_single={preserve_single}, threshold={dual_threshold}")

    def get_processing_recommendations(self, face_data_sequence: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get processing recommendations based on face data analysis
        
        Args:
            face_data_sequence: Sequence of face detection data
            
        Returns:
            Processing recommendations and analysis
        """
        analysis = self._analyze_face_data_sequence(face_data_sequence)
        
        recommendations = {
            'analysis': analysis,
            'recommended_approach': analysis['recommendation'],
            'dual_output_beneficial': analysis['is_dual_face_scenario'],
            'processing_options': {
                'dual_only': analysis['is_dual_face_scenario'],
                'single_only': not analysis['is_dual_face_scenario'],
                'both_outputs': analysis['is_dual_face_scenario'] and self.preserve_single_output
            },
            'expected_benefits': []
        }
        
        if analysis['is_dual_face_scenario']:
            recommendations['expected_benefits'].extend([
                'Optimal framing for each face',
                'Elimination of face overlap issues',
                'Enhanced predictive tracking per face',
                'Synchronized dual outputs for compositing'
            ])
        
        return recommendations

    def get_debug_info(self) -> Dict[str, Any]:
        """Get comprehensive debug information"""
        return {
            'enhanced_dual_positioning': {
                'dual_face_threshold': self.dual_face_threshold,
                'enable_dual_output': self.enable_dual_output,
                'preserve_single_output': self.preserve_single_output,
                'last_face_count': self.last_face_count,
                'dual_face_detection_active': self.dual_face_detection_active
            },
            'face_positioning_engine': self.face_positioning_engine.get_debug_info(),
            'dual_video_compositor': self.dual_video_compositor.get_debug_info()
        }
