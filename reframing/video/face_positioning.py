#!/usr/bin/env python3
"""
Advanced Face Positioning Engine for Temporal Video Generation

This module implements sophisticated face positioning logic with specific layout requirements:
- Single face (1): Center both horizontally and vertically
- Two faces (2): Vertical arrangement with leftmost in top half, rightmost in bottom half
- Three faces (3): 2+1 layout with two faces in top half, one centered in bottom half
- Four faces (4): 2x2 grid layout

Features:
- MediaPipe integration with GPU acceleration and CPU fallback
- Predictive face tracking with 2-3 second lookahead and comprehensive segment planning
- Smooth transitions when face count changes
- Aspect ratio preservation for detected face regions
- Professional terminology and enterprise-grade performance
"""

import logging
import numpy as np
from typing import List, Tuple, Optional, Dict, Any, Deque
from dataclasses import dataclass, field
from enum import Enum
from collections import deque

try:
    from ..models.data_classes import FaceDetection, GroupFaceBounds
except ImportError:
    # Fallback for testing
    from dataclasses import dataclass as _dataclass

    @_dataclass
    class FaceDetection:
        x: int
        y: int
        width: int
        height: int
        confidence: float
        center_x: float
        center_y: float

    @_dataclass
    class GroupFaceBounds:
        x: int
        y: int
        width: int
        height: int
        center_x: float
        center_y: float
        face_count: int
        confidence: float


class FaceLayoutType(Enum):
    """Face layout types for different face counts"""
    SINGLE_CENTERED = "single_centered"
    DUAL_VERTICAL = "dual_vertical"
    TRIPLE_2_PLUS_1 = "triple_2_plus_1"
    QUAD_GRID = "quad_grid"


@dataclass
class FaceRegion:
    """Represents a designated region for face positioning"""
    x: int
    y: int
    width: int
    height: int
    center_x: float
    center_y: float
    region_id: str  # e.g., "top_left", "bottom_center", etc.


@dataclass
class PredictiveFaceData:
    """Face data with predictive information for lookahead analysis"""
    face: FaceDetection
    predicted_positions: List[Tuple[float, float]]  # Future positions for next 2-3 seconds
    stability_score: float  # How stable this face is expected to be
    track_id: Optional[int] = None
    velocity: Tuple[float, float] = (0.0, 0.0)


@dataclass
class SegmentPlan:
    """Comprehensive segment plan for predictive face tracking"""
    start_time: float
    end_time: float
    layout_type: FaceLayoutType
    expected_face_count: int
    stability_score: float
    transition_required: bool = False
    confidence: float = 1.0


@dataclass
class FacePositionLayout:
    """Complete face positioning layout for a frame"""
    layout_type: FaceLayoutType
    regions: List[FaceRegion]
    face_assignments: Dict[int, str]  # face_index -> region_id
    crop_x: int
    crop_y: int
    confidence: float
    segment_plan: Optional[SegmentPlan] = None
    predictive_data: List[PredictiveFaceData] = field(default_factory=list)


class FacePositioningEngine:
    """
    Advanced face positioning engine with sophisticated layout algorithms

    Implements enterprise-grade face positioning with predictive tracking,
    smooth transitions, and optimal composition for temporal video generation.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Layout configuration constants
        self.REGION_MARGIN = 0.05  # 5% margin within each region
        self.TRANSITION_SMOOTHING = 0.3  # Smoothing factor for layout transitions
        self.FACE_ASPECT_TOLERANCE = 0.2  # Tolerance for face aspect ratio preservation

        # Predictive tracking configuration
        self.LOOKAHEAD_DURATION = 2.5  # 2.5 seconds lookahead for segment planning
        self.PREDICTION_INTERVAL = 0.1  # Predict every 100ms for smooth analysis
        self.STABILITY_THRESHOLD = 0.7  # Minimum stability score for reliable predictions
        self.FACE_CONSISTENCY_WINDOW = 1.0  # 1 second window for face consistency analysis

        # Previous layout for smooth transitions
        self.previous_layout: Optional[FacePositionLayout] = None
        self.layout_transition_frames = 0
        self.max_transition_frames = 10  # Frames to smooth layout transitions

        # Predictive tracking state
        self.face_history: Deque[Tuple[float, List[FaceDetection]]] = deque(maxlen=30)  # 3 seconds at 10fps
        self.segment_plans: List[SegmentPlan] = []
        self.current_segment_index = 0
        
    def calculate_face_positioning(self, faces: List[FaceDetection],
                                 frame_width: int, frame_height: int,
                                 target_width: int, target_height: int,
                                 timestamp: float) -> FacePositionLayout:
        """
        Calculate optimal face positioning with predictive analysis and segment planning

        Args:
            faces: List of detected faces sorted by detection confidence
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height
            timestamp: Current timestamp for temporal consistency

        Returns:
            FacePositionLayout with complete positioning information and predictive data
        """
        # Update face history for predictive analysis
        self.face_history.append((timestamp, faces.copy()))

        face_count = len(faces)

        if face_count == 0:
            return self._create_fallback_layout(target_width, target_height)

        # Perform predictive analysis for 2-3 second lookahead
        predictive_data = self._analyze_predictive_face_data(faces, timestamp)

        # Generate or update segment plans based on predictive analysis
        current_segment = self._update_segment_plans(faces, timestamp, predictive_data, face_count)

        # Sort faces by position for consistent assignment (enhanced with predictive stability)
        sorted_faces = self._sort_faces_for_layout_with_prediction(faces, predictive_data)

        # Determine layout type based on face count and segment plan
        layout_type = self._determine_layout_type_with_prediction(face_count, current_segment)

        # Create regions for the layout
        regions = self._create_layout_regions(layout_type, target_width, target_height)

        # Assign faces to regions with predictive consistency
        face_assignments = self._assign_faces_to_regions_with_prediction(
            sorted_faces, regions, layout_type, predictive_data
        )

        # Calculate optimal crop position with predictive smoothing
        crop_x, crop_y = self._calculate_optimal_crop_position_with_prediction(
            sorted_faces, regions, face_assignments, frame_width, frame_height,
            target_width, target_height, predictive_data
        )

        # Calculate layout confidence with predictive factors
        confidence = self._calculate_layout_confidence_with_prediction(
            sorted_faces, regions, face_assignments, predictive_data
        )

        layout = FacePositionLayout(
            layout_type=layout_type,
            regions=regions,
            face_assignments=face_assignments,
            crop_x=crop_x,
            crop_y=crop_y,
            confidence=confidence,
            segment_plan=current_segment,
            predictive_data=predictive_data
        )

        # Apply smooth transitions with predictive awareness
        if self.previous_layout and self.previous_layout.layout_type != layout_type:
            layout = self._apply_predictive_layout_transition(layout, timestamp)

        self.previous_layout = layout
        return layout

    def _determine_layout_type(self, face_count: int) -> FaceLayoutType:
        """Determine layout type based on face count"""
        if face_count == 1:
            return FaceLayoutType.SINGLE_CENTERED
        elif face_count == 2:
            return FaceLayoutType.DUAL_VERTICAL
        elif face_count == 3:
            return FaceLayoutType.TRIPLE_2_PLUS_1
        elif face_count >= 4:
            return FaceLayoutType.QUAD_GRID
        else:
            return FaceLayoutType.SINGLE_CENTERED  # Fallback

    def _sort_faces_for_layout(self, faces: List[FaceDetection]) -> List[FaceDetection]:
        """
        Sort faces for consistent layout assignment

        For 2 faces: leftmost first, rightmost second
        For 3+ faces: left-to-right, top-to-bottom order
        """
        if len(faces) <= 1:
            return faces

        # Sort by x-coordinate (left to right) as primary, y-coordinate as secondary
        return sorted(faces, key=lambda f: (f.x, f.y))

    def _create_layout_regions(self, layout_type: FaceLayoutType,
                             target_width: int, target_height: int) -> List[FaceRegion]:
        """Create regions for the specified layout type"""

        if layout_type == FaceLayoutType.SINGLE_CENTERED:
            return self._create_single_centered_regions(target_width, target_height)
        elif layout_type == FaceLayoutType.DUAL_VERTICAL:
            return self._create_dual_vertical_regions(target_width, target_height)
        elif layout_type == FaceLayoutType.TRIPLE_2_PLUS_1:
            return self._create_triple_2_plus_1_regions(target_width, target_height)
        elif layout_type == FaceLayoutType.QUAD_GRID:
            return self._create_quad_grid_regions(target_width, target_height)
        else:
            return self._create_single_centered_regions(target_width, target_height)

    def _create_single_centered_regions(self, target_width: int, target_height: int) -> List[FaceRegion]:
        """Create single centered region for one face"""
        region = FaceRegion(
            x=0,
            y=0,
            width=target_width,
            height=target_height,
            center_x=target_width / 2.0,
            center_y=target_height / 2.0,
            region_id="center"
        )
        return [region]

    def _create_dual_vertical_regions(self, target_width: int, target_height: int) -> List[FaceRegion]:
        """
        Create vertical arrangement regions for two faces
        - Leftmost face in top half
        - Rightmost face in bottom half
        """
        half_height = target_height // 2

        top_region = FaceRegion(
            x=0,
            y=0,
            width=target_width,
            height=half_height,
            center_x=target_width / 2.0,
            center_y=half_height / 2.0,
            region_id="top_half"
        )

        bottom_region = FaceRegion(
            x=0,
            y=half_height,
            width=target_width,
            height=half_height,
            center_x=target_width / 2.0,
            center_y=half_height + half_height / 2.0,
            region_id="bottom_half"
        )

        return [top_region, bottom_region]

    def _create_triple_2_plus_1_regions(self, target_width: int, target_height: int) -> List[FaceRegion]:
        """
        Create 2+1 layout regions for three faces
        - Two faces in top half (left and right positions)
        - One face centered in bottom half
        """
        half_width = target_width // 2
        half_height = target_height // 2

        top_left_region = FaceRegion(
            x=0,
            y=0,
            width=half_width,
            height=half_height,
            center_x=half_width / 2.0,
            center_y=half_height / 2.0,
            region_id="top_left"
        )

        top_right_region = FaceRegion(
            x=half_width,
            y=0,
            width=half_width,
            height=half_height,
            center_x=half_width + half_width / 2.0,
            center_y=half_height / 2.0,
            region_id="top_right"
        )

        bottom_center_region = FaceRegion(
            x=0,
            y=half_height,
            width=target_width,
            height=half_height,
            center_x=target_width / 2.0,
            center_y=half_height + half_height / 2.0,
            region_id="bottom_center"
        )

        return [top_left_region, top_right_region, bottom_center_region]

    def _create_quad_grid_regions(self, target_width: int, target_height: int) -> List[FaceRegion]:
        """
        Create 2x2 grid layout regions for four faces
        - Two faces in top half (left and right)
        - Two faces in bottom half (left and right)
        """
        half_width = target_width // 2
        half_height = target_height // 2

        top_left_region = FaceRegion(
            x=0,
            y=0,
            width=half_width,
            height=half_height,
            center_x=half_width / 2.0,
            center_y=half_height / 2.0,
            region_id="top_left"
        )

        top_right_region = FaceRegion(
            x=half_width,
            y=0,
            width=half_width,
            height=half_height,
            center_x=half_width + half_width / 2.0,
            center_y=half_height / 2.0,
            region_id="top_right"
        )

        bottom_left_region = FaceRegion(
            x=0,
            y=half_height,
            width=half_width,
            height=half_height,
            center_x=half_width / 2.0,
            center_y=half_height + half_height / 2.0,
            region_id="bottom_left"
        )

        bottom_right_region = FaceRegion(
            x=half_width,
            y=half_height,
            width=half_width,
            height=half_height,
            center_x=half_width + half_width / 2.0,
            center_y=half_height + half_height / 2.0,
            region_id="bottom_right"
        )

        return [top_left_region, top_right_region, bottom_left_region, bottom_right_region]

    def _assign_faces_to_regions(self, faces: List[FaceDetection],
                               regions: List[FaceRegion],
                               layout_type: FaceLayoutType) -> Dict[int, str]:
        """Assign faces to regions based on layout type and face positions"""

        if layout_type == FaceLayoutType.SINGLE_CENTERED:
            return {0: "center"} if faces else {}

        elif layout_type == FaceLayoutType.DUAL_VERTICAL:
            # Leftmost face goes to top half, rightmost to bottom half
            if len(faces) >= 2:
                return {0: "top_half", 1: "bottom_half"}
            elif len(faces) == 1:
                return {0: "top_half"}  # Single face goes to top
            return {}

        elif layout_type == FaceLayoutType.TRIPLE_2_PLUS_1:
            # First two faces go to top left/right, third to bottom center
            assignments = {}
            if len(faces) >= 1:
                assignments[0] = "top_left"
            if len(faces) >= 2:
                assignments[1] = "top_right"
            if len(faces) >= 3:
                assignments[2] = "bottom_center"
            return assignments

        elif layout_type == FaceLayoutType.QUAD_GRID:
            # Assign faces to 2x2 grid in order
            region_order = ["top_left", "top_right", "bottom_left", "bottom_right"]
            assignments = {}
            for i, face in enumerate(faces[:4]):  # Limit to 4 faces
                assignments[i] = region_order[i]
            return assignments

        return {}

    def _calculate_optimal_crop_position(self, faces: List[FaceDetection],
                                       regions: List[FaceRegion],
                                       face_assignments: Dict[int, str],
                                       frame_width: int, frame_height: int,
                                       target_width: int, target_height: int) -> Tuple[int, int]:
        """Calculate optimal crop position to center faces within their assigned regions"""

        if not faces or not face_assignments:
            # Fallback to center crop
            return (frame_width - target_width) // 2, (frame_height - target_height) // 2

        # Calculate the center of mass of all assigned faces
        total_weight = 0
        weighted_center_x = 0
        weighted_center_y = 0

        for face_idx, region_id in face_assignments.items():
            if face_idx < len(faces):
                face = faces[face_idx]
                region = next((r for r in regions if r.region_id == region_id), None)

                if region:
                    # Calculate where the face should be positioned within its region
                    target_face_x = region.center_x
                    target_face_y = region.center_y

                    # Weight by face confidence and size
                    face_weight = face.confidence * (face.width * face.height)

                    weighted_center_x += target_face_x * face_weight
                    weighted_center_y += target_face_y * face_weight
                    total_weight += face_weight

        if total_weight > 0:
            target_center_x = weighted_center_x / total_weight
            target_center_y = weighted_center_y / total_weight
        else:
            target_center_x = target_width / 2.0
            target_center_y = target_height / 2.0

        # Calculate crop position to achieve the target center
        # We need to find where to position the crop so that the faces align with their regions
        crop_x = 0
        crop_y = 0

        # Use the primary face (highest confidence) to determine crop position
        primary_face_idx = max(face_assignments.keys(),
                             key=lambda i: faces[i].confidence if i < len(faces) else 0)

        if primary_face_idx < len(faces):
            primary_face = faces[primary_face_idx]
            primary_region_id = face_assignments[primary_face_idx]
            primary_region = next((r for r in regions if r.region_id == primary_region_id), None)

            if primary_region:
                # Calculate crop position to center the primary face in its region
                face_center_x = primary_face.x + primary_face.width / 2.0
                face_center_y = primary_face.y + primary_face.height / 2.0

                crop_x = int(face_center_x - primary_region.center_x)
                crop_y = int(face_center_y - primary_region.center_y)

        # Ensure crop is within frame bounds
        crop_x = max(0, min(frame_width - target_width, crop_x))
        crop_y = max(0, min(frame_height - target_height, crop_y))

        return crop_x, crop_y

    def _calculate_layout_confidence(self, faces: List[FaceDetection],
                                   regions: List[FaceRegion],
                                   face_assignments: Dict[int, str]) -> float:
        """Calculate confidence score for the layout"""

        if not faces or not face_assignments:
            return 0.0

        total_confidence = 0.0
        total_weight = 0.0

        for face_idx, region_id in face_assignments.items():
            if face_idx < len(faces):
                face = faces[face_idx]
                region = next((r for r in regions if r.region_id == region_id), None)

                if region:
                    # Base confidence from face detection
                    face_confidence = face.confidence

                    # Bonus for face size (larger faces are more reliable)
                    face_area = face.width * face.height
                    size_bonus = min(0.2, face_area / 10000)  # Up to 20% bonus

                    # Penalty for faces too close to edges (may be partially cut off)
                    edge_penalty = 0.0
                    if face.x < 50 or face.y < 50:  # Too close to edges
                        edge_penalty = 0.1

                    adjusted_confidence = face_confidence + size_bonus - edge_penalty
                    adjusted_confidence = max(0.0, min(1.0, adjusted_confidence))

                    total_confidence += adjusted_confidence
                    total_weight += 1.0

        return total_confidence / total_weight if total_weight > 0 else 0.0

    def _create_fallback_layout(self, target_width: int, target_height: int) -> FacePositionLayout:
        """Create fallback layout when no faces are detected"""
        regions = self._create_single_centered_regions(target_width, target_height)

        return FacePositionLayout(
            layout_type=FaceLayoutType.SINGLE_CENTERED,
            regions=regions,
            face_assignments={},
            crop_x=0,
            crop_y=0,
            confidence=0.0
        )

    def _apply_layout_transition(self, new_layout: FacePositionLayout,
                               timestamp: float) -> FacePositionLayout:
        """
        Apply smooth transition when layout type changes

        This helps prevent jarring jumps when the number of detected faces changes
        """
        if not self.previous_layout:
            return new_layout

        # Increment transition frame counter
        self.layout_transition_frames += 1

        # If we've completed the transition, use the new layout
        if self.layout_transition_frames >= self.max_transition_frames:
            self.layout_transition_frames = 0
            return new_layout

        # Calculate transition progress (0.0 to 1.0)
        progress = self.layout_transition_frames / self.max_transition_frames

        # Smooth interpolation between old and new crop positions
        old_crop_x = self.previous_layout.crop_x
        old_crop_y = self.previous_layout.crop_y
        new_crop_x = new_layout.crop_x
        new_crop_y = new_layout.crop_y

        # Apply easing function for smoother transitions
        eased_progress = self._ease_in_out_cubic(progress)

        interpolated_crop_x = int(old_crop_x + (new_crop_x - old_crop_x) * eased_progress)
        interpolated_crop_y = int(old_crop_y + (new_crop_y - old_crop_y) * eased_progress)

        # Create transition layout
        transition_layout = FacePositionLayout(
            layout_type=new_layout.layout_type,
            regions=new_layout.regions,
            face_assignments=new_layout.face_assignments,
            crop_x=interpolated_crop_x,
            crop_y=interpolated_crop_y,
            confidence=new_layout.confidence * progress + self.previous_layout.confidence * (1 - progress)
        )

        self.logger.debug(f"Layout transition progress: {progress:.2f}, "
                         f"crop: ({interpolated_crop_x}, {interpolated_crop_y})")

        return transition_layout

    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic easing function for smooth transitions"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2

    def get_face_region_for_crop(self, face: FaceDetection, layout: FacePositionLayout) -> Optional[FaceRegion]:
        """
        Get the target region for a face within the crop window

        This is useful for validating that faces are properly positioned within their regions
        """
        # Find which face index this corresponds to
        face_idx = None
        for idx, region_id in layout.face_assignments.items():
            # This is a simplified check - in practice you'd want more robust face matching
            if idx < len(layout.regions):
                face_idx = idx
                break

        if face_idx is not None and face_idx in layout.face_assignments:
            region_id = layout.face_assignments[face_idx]
            return next((r for r in layout.regions if r.region_id == region_id), None)

        return None

    def _analyze_predictive_face_data(self, faces: List[FaceDetection],
                                    timestamp: float) -> List[PredictiveFaceData]:
        """
        Analyze face data to generate predictive information for 2-3 second lookahead

        Args:
            faces: Current detected faces
            timestamp: Current timestamp

        Returns:
            List of predictive face data with future position predictions
        """
        predictive_data = []

        for face in faces:
            # Calculate velocity and stability from face history
            velocity, stability_score = self._calculate_face_velocity_and_stability(face, timestamp)

            # Generate predicted positions for the next 2.5 seconds
            predicted_positions = self._predict_future_positions(face, velocity, timestamp)

            # Create predictive face data
            pred_data = PredictiveFaceData(
                face=face,
                predicted_positions=predicted_positions,
                stability_score=stability_score,
                velocity=velocity
            )

            predictive_data.append(pred_data)

        return predictive_data

    def _calculate_face_velocity_and_stability(self, face: FaceDetection,
                                             timestamp: float) -> Tuple[Tuple[float, float], float]:
        """
        Calculate face velocity and stability score from historical data

        Args:
            face: Current face detection
            timestamp: Current timestamp

        Returns:
            Tuple of (velocity_x, velocity_y) and stability_score
        """
        if len(self.face_history) < 3:
            return (0.0, 0.0), 1.0  # No movement, high stability

        # Find matching faces in history (within reasonable distance)
        matching_faces = []
        for hist_timestamp, hist_faces in self.face_history:
            if timestamp - hist_timestamp > self.FACE_CONSISTENCY_WINDOW:
                continue

            # Find closest face by position
            closest_face = None
            min_distance = float('inf')

            for hist_face in hist_faces:
                distance = np.sqrt((face.center_x - hist_face.center_x)**2 +
                                 (face.center_y - hist_face.center_y)**2)
                if distance < min_distance and distance < 100:  # Within 100 pixels
                    min_distance = distance
                    closest_face = hist_face

            if closest_face:
                matching_faces.append((hist_timestamp, closest_face))

        if len(matching_faces) < 2:
            return (0.0, 0.0), 0.8  # Limited history, moderate stability

        # Calculate velocity from recent positions
        velocities = []
        for i in range(1, len(matching_faces)):
            prev_time, prev_face = matching_faces[i-1]
            curr_time, curr_face = matching_faces[i]

            dt = curr_time - prev_time
            if dt > 0:
                vx = (curr_face.center_x - prev_face.center_x) / dt
                vy = (curr_face.center_y - prev_face.center_y) / dt
                velocities.append((vx, vy))

        if not velocities:
            return (0.0, 0.0), 0.8

        # Average velocity
        avg_vx = sum(v[0] for v in velocities) / len(velocities)
        avg_vy = sum(v[1] for v in velocities) / len(velocities)

        # Calculate stability score based on velocity consistency
        velocity_variance = np.var([np.sqrt(v[0]**2 + v[1]**2) for v in velocities])
        stability_score = max(0.1, 1.0 - min(1.0, velocity_variance / 1000))  # Normalize variance

        return (avg_vx, avg_vy), stability_score

    def _predict_future_positions(self, face: FaceDetection, velocity: Tuple[float, float],
                                timestamp: float) -> List[Tuple[float, float]]:
        """
        Predict future face positions based on current position and velocity

        Args:
            face: Current face detection
            velocity: Face velocity (vx, vy)
            timestamp: Current timestamp

        Returns:
            List of predicted positions for the next 2.5 seconds
        """
        predictions = []
        vx, vy = velocity

        # Predict positions at 100ms intervals for next 2.5 seconds
        for i in range(1, 26):  # 25 predictions (2.5 seconds)
            dt = i * self.PREDICTION_INTERVAL

            # Simple linear prediction with damping for long-term stability
            damping_factor = max(0.1, 1.0 - (dt / 5.0))  # Reduce confidence over time

            pred_x = face.center_x + (vx * dt * damping_factor)
            pred_y = face.center_y + (vy * dt * damping_factor)

            predictions.append((pred_x, pred_y))

        return predictions

    def _update_segment_plans(self, faces: List[FaceDetection], timestamp: float,
                            predictive_data: List[PredictiveFaceData],
                            actual_face_count: int) -> Optional[SegmentPlan]:
        """
        Generate or update segment plans based on predictive analysis

        Args:
            faces: Current detected faces
            timestamp: Current timestamp
            predictive_data: Predictive face data

        Returns:
            Current segment plan or None
        """
        # Analyze face count stability over the lookahead period
        face_count_predictions = self._predict_face_count_changes(predictive_data, timestamp)

        # Determine if we need to create new segment plans
        if not self.segment_plans or self._should_update_segment_plans(timestamp, face_count_predictions):
            self.segment_plans = self._generate_segment_plans(timestamp, face_count_predictions, predictive_data, actual_face_count)
            self.current_segment_index = 0

        # Find current active segment
        current_segment = None
        for i, segment in enumerate(self.segment_plans):
            if segment.start_time <= timestamp <= segment.end_time:
                current_segment = segment
                self.current_segment_index = i
                break

        return current_segment

    def _predict_face_count_changes(self, predictive_data: List[PredictiveFaceData],
                                  timestamp: float) -> List[Tuple[float, int]]:
        """
        Predict face count changes over the lookahead period

        Args:
            predictive_data: Predictive face data
            timestamp: Current timestamp

        Returns:
            List of (time, expected_face_count) tuples
        """
        predictions = []

        # Analyze stability of each face over the prediction window
        for i in range(0, 25, 5):  # Check every 500ms
            future_time = timestamp + (i * self.PREDICTION_INTERVAL)
            stable_faces = 0

            for pred_data in predictive_data:
                if i < len(pred_data.predicted_positions):
                    # Check if face is likely to remain stable at this time
                    if pred_data.stability_score > self.STABILITY_THRESHOLD:
                        stable_faces += 1

            predictions.append((future_time, stable_faces))

        return predictions

    def _should_update_segment_plans(self, timestamp: float,
                                   face_count_predictions: List[Tuple[float, int]]) -> bool:
        """
        Determine if segment plans need to be updated

        Args:
            timestamp: Current timestamp
            face_count_predictions: Predicted face count changes

        Returns:
            True if plans should be updated
        """
        if not self.segment_plans:
            return True

        # Check if we're near the end of current plans
        last_segment_end = max(seg.end_time for seg in self.segment_plans)
        if timestamp > last_segment_end - 1.0:  # Within 1 second of end
            return True

        # Check if predicted face counts differ significantly from current plans
        for future_time, predicted_count in face_count_predictions:
            for segment in self.segment_plans:
                if segment.start_time <= future_time <= segment.end_time:
                    if abs(predicted_count - segment.expected_face_count) > 1:
                        return True
                    break

        return False

    def _generate_segment_plans(self, timestamp: float,
                              face_count_predictions: List[Tuple[float, int]],
                              predictive_data: List[PredictiveFaceData],
                              actual_face_count: int) -> List[SegmentPlan]:
        """
        Generate comprehensive segment plans for the lookahead period

        Args:
            timestamp: Current timestamp
            face_count_predictions: Predicted face count changes
            predictive_data: Predictive face data

        Returns:
            List of segment plans
        """
        segments = []

        if not face_count_predictions:
            # Fallback: single segment with current state using actual face count
            layout_type = self._determine_layout_type(actual_face_count)

            segment = SegmentPlan(
                start_time=timestamp,
                end_time=timestamp + self.LOOKAHEAD_DURATION,
                layout_type=layout_type,
                expected_face_count=actual_face_count,
                stability_score=1.0,
                confidence=0.8
            )
            return [segment]

        # Group predictions by face count to create stable segments
        current_count = face_count_predictions[0][1]
        segment_start = timestamp

        for i, (pred_time, pred_count) in enumerate(face_count_predictions):
            # Check if face count changed significantly
            if pred_count != current_count or i == len(face_count_predictions) - 1:
                # Create segment for previous count
                if i > 0 or pred_count == current_count:
                    segment_end = pred_time if pred_count != current_count else pred_time
                    layout_type = self._determine_layout_type(current_count)

                    # Calculate stability score for this segment
                    stability_score = self._calculate_segment_stability(
                        predictive_data, segment_start, segment_end
                    )

                    segment = SegmentPlan(
                        start_time=segment_start,
                        end_time=segment_end,
                        layout_type=layout_type,
                        expected_face_count=current_count,
                        stability_score=stability_score,
                        transition_required=(len(segments) > 0 and
                                           segments[-1].layout_type != layout_type),
                        confidence=min(1.0, stability_score + 0.2)
                    )
                    segments.append(segment)

                # Start new segment
                if pred_count != current_count:
                    current_count = pred_count
                    segment_start = pred_time

        # Ensure we have at least one segment
        if not segments:
            layout_type = self._determine_layout_type(actual_face_count)
            segment = SegmentPlan(
                start_time=timestamp,
                end_time=timestamp + self.LOOKAHEAD_DURATION,
                layout_type=layout_type,
                expected_face_count=actual_face_count,
                stability_score=0.8,
                confidence=0.8
            )
            segments.append(segment)

        return segments

    def _calculate_segment_stability(self, predictive_data: List[PredictiveFaceData],
                                   start_time: float, end_time: float) -> float:
        """
        Calculate stability score for a segment based on predictive data

        Args:
            predictive_data: Predictive face data
            start_time: Segment start time
            end_time: Segment end time

        Returns:
            Stability score (0.0 to 1.0)
        """
        if not predictive_data:
            return 0.5

        # Average stability scores of all faces in the segment
        total_stability = sum(pred.stability_score for pred in predictive_data)
        avg_stability = total_stability / len(predictive_data)

        # Bonus for longer segments (more stable)
        duration = end_time - start_time
        duration_bonus = min(0.2, duration / 5.0)  # Up to 20% bonus for 5+ second segments

        return min(1.0, avg_stability + duration_bonus)

    def _sort_faces_for_layout_with_prediction(self, faces: List[FaceDetection],
                                             predictive_data: List[PredictiveFaceData]) -> List[FaceDetection]:
        """
        Enhanced face sorting that considers predictive stability for consistent assignment

        Args:
            faces: Current detected faces
            predictive_data: Predictive face data

        Returns:
            Sorted faces with stability-aware ordering
        """
        if len(faces) <= 1:
            return faces

        # Create face-prediction pairs
        face_pred_pairs = []
        for i, face in enumerate(faces):
            pred_data = predictive_data[i] if i < len(predictive_data) else None
            stability = pred_data.stability_score if pred_data else 0.5
            face_pred_pairs.append((face, stability))

        # Sort by position (left-to-right, top-to-bottom) with stability as secondary factor
        # More stable faces get slight preference in positioning
        sorted_pairs = sorted(face_pred_pairs,
                            key=lambda fp: (fp[0].x, fp[0].y, -fp[1]))  # Negative stability for descending order

        return [face for face, _ in sorted_pairs]

    def _determine_layout_type_with_prediction(self, face_count: int,
                                             segment_plan: Optional[SegmentPlan]) -> FaceLayoutType:
        """
        Enhanced layout type determination using segment plan information

        Args:
            face_count: Current face count
            segment_plan: Current segment plan

        Returns:
            Layout type considering predictive information
        """
        # Use segment plan if available, reliable, and matches current face count
        if (segment_plan and segment_plan.confidence > 0.8 and
            segment_plan.expected_face_count == face_count):
            return segment_plan.layout_type

        # Fallback to standard determination based on actual face count
        return self._determine_layout_type(face_count)

    def _assign_faces_to_regions_with_prediction(self, faces: List[FaceDetection],
                                               regions: List[FaceRegion],
                                               layout_type: FaceLayoutType,
                                               predictive_data: List[PredictiveFaceData]) -> Dict[int, str]:
        """
        Enhanced face-to-region assignment using predictive stability

        Args:
            faces: Sorted faces
            regions: Layout regions
            layout_type: Current layout type
            predictive_data: Predictive face data

        Returns:
            Face assignments with predictive consistency
        """
        # Start with standard assignment
        base_assignments = self._assign_faces_to_regions(faces, regions, layout_type)

        # Enhance with predictive consistency if we have previous layout
        if self.previous_layout and self.previous_layout.face_assignments:
            # Try to maintain consistent assignments for stable faces
            enhanced_assignments = self._maintain_assignment_consistency(
                faces, base_assignments, predictive_data
            )
            return enhanced_assignments

        return base_assignments

    def _maintain_assignment_consistency(self, faces: List[FaceDetection],
                                       base_assignments: Dict[int, str],
                                       predictive_data: List[PredictiveFaceData]) -> Dict[int, str]:
        """
        Maintain assignment consistency for stable faces across frames

        Args:
            faces: Current faces
            base_assignments: Base face assignments
            predictive_data: Predictive face data

        Returns:
            Enhanced assignments with consistency
        """
        if not self.previous_layout or not self.previous_layout.face_assignments:
            return base_assignments

        enhanced_assignments = base_assignments.copy()

        # For highly stable faces, try to maintain their previous region assignment
        for i, face in enumerate(faces):
            if i >= len(predictive_data):
                continue

            pred_data = predictive_data[i]
            if pred_data.stability_score > 0.9:  # Very stable face
                # Find if this face had a consistent assignment in previous frame
                # This is a simplified approach - in production, you'd use face tracking IDs
                prev_assignment = self._find_previous_assignment_for_face(face)
                if prev_assignment and prev_assignment in [r.region_id for r in self.previous_layout.regions]:
                    # Check if this assignment is still valid for current layout
                    if self._is_assignment_valid(i, prev_assignment, enhanced_assignments):
                        enhanced_assignments[i] = prev_assignment

        return enhanced_assignments

    def _find_previous_assignment_for_face(self, face: FaceDetection) -> Optional[str]:
        """
        Find previous region assignment for a face based on position similarity

        Args:
            face: Current face detection

        Returns:
            Previous region assignment or None
        """
        if not self.previous_layout or not hasattr(self.previous_layout, 'predictive_data'):
            return None

        # Find closest face from previous frame
        min_distance = float('inf')
        closest_assignment = None

        for prev_pred_data in self.previous_layout.predictive_data:
            prev_face = prev_pred_data.face
            distance = np.sqrt((face.center_x - prev_face.center_x)**2 +
                             (face.center_y - prev_face.center_y)**2)

            if distance < min_distance and distance < 50:  # Within 50 pixels
                min_distance = distance
                # Find assignment for this face
                for face_idx, region_id in self.previous_layout.face_assignments.items():
                    if face_idx < len(self.previous_layout.predictive_data):
                        if self.previous_layout.predictive_data[face_idx].face == prev_face:
                            closest_assignment = region_id
                            break

        return closest_assignment

    def _is_assignment_valid(self, face_idx: int, region_id: str,
                           current_assignments: Dict[int, str]) -> bool:
        """
        Check if a region assignment is valid (not already taken by another face)

        Args:
            face_idx: Face index
            region_id: Proposed region ID
            current_assignments: Current assignments

        Returns:
            True if assignment is valid
        """
        # Check if region is already assigned to another face
        for other_idx, other_region in current_assignments.items():
            if other_idx != face_idx and other_region == region_id:
                return False

        return True

    def _calculate_optimal_crop_position_with_prediction(self, faces: List[FaceDetection],
                                                       regions: List[FaceRegion],
                                                       face_assignments: Dict[int, str],
                                                       frame_width: int, frame_height: int,
                                                       target_width: int, target_height: int,
                                                       predictive_data: List[PredictiveFaceData]) -> Tuple[int, int]:
        """
        Enhanced crop position calculation using predictive smoothing

        Args:
            faces: Current faces
            regions: Layout regions
            face_assignments: Face assignments
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height
            predictive_data: Predictive face data

        Returns:
            Optimal crop position (x, y)
        """
        # Start with standard calculation
        base_crop_x, base_crop_y = self._calculate_optimal_crop_position(
            faces, regions, face_assignments, frame_width, frame_height, target_width, target_height
        )

        # Apply predictive smoothing if we have reliable predictions
        if predictive_data and len(predictive_data) > 0:
            # Calculate predicted crop position for next few frames
            predicted_crop = self._calculate_predicted_crop_position(
                predictive_data, regions, face_assignments, target_width, target_height
            )

            if predicted_crop:
                pred_x, pred_y = predicted_crop

                # Blend current and predicted positions based on prediction confidence
                avg_stability = sum(p.stability_score for p in predictive_data) / len(predictive_data)
                blend_factor = min(0.3, avg_stability * 0.4)  # Max 30% blending

                smoothed_x = int(base_crop_x * (1 - blend_factor) + pred_x * blend_factor)
                smoothed_y = int(base_crop_y * (1 - blend_factor) + pred_y * blend_factor)

                # Ensure bounds
                smoothed_x = max(0, min(frame_width - target_width, smoothed_x))
                smoothed_y = max(0, min(frame_height - target_height, smoothed_y))

                return smoothed_x, smoothed_y

        return base_crop_x, base_crop_y

    def _calculate_predicted_crop_position(self, predictive_data: List[PredictiveFaceData],
                                         regions: List[FaceRegion],
                                         face_assignments: Dict[int, str],
                                         target_width: int, target_height: int) -> Optional[Tuple[int, int]]:
        """
        Calculate predicted crop position based on face movement predictions

        Args:
            predictive_data: Predictive face data
            regions: Layout regions
            face_assignments: Face assignments
            target_width: Target crop width
            target_height: Target crop height

        Returns:
            Predicted crop position or None
        """
        if not predictive_data or not face_assignments:
            return None

        # Use predictions from 0.5 seconds ahead (5 prediction steps)
        prediction_step = 5

        total_weight = 0
        weighted_center_x = 0
        weighted_center_y = 0

        for face_idx, region_id in face_assignments.items():
            if face_idx >= len(predictive_data):
                continue

            pred_data = predictive_data[face_idx]
            if prediction_step >= len(pred_data.predicted_positions):
                continue

            region = next((r for r in regions if r.region_id == region_id), None)
            if not region:
                continue

            # Get predicted position
            pred_x, pred_y = pred_data.predicted_positions[prediction_step]

            # Calculate where this face should be positioned within its region
            target_face_x = region.center_x
            target_face_y = region.center_y

            # Weight by face stability and size
            face_weight = pred_data.stability_score * (pred_data.face.width * pred_data.face.height)

            weighted_center_x += target_face_x * face_weight
            weighted_center_y += target_face_y * face_weight
            total_weight += face_weight

        if total_weight > 0:
            target_center_x = weighted_center_x / total_weight
            target_center_y = weighted_center_y / total_weight

            # Calculate crop position to achieve the target center
            # Use the primary face for crop calculation
            primary_face_idx = max(face_assignments.keys(),
                                 key=lambda i: predictive_data[i].stability_score if i < len(predictive_data) else 0)

            if primary_face_idx < len(predictive_data):
                pred_data = predictive_data[primary_face_idx]
                if prediction_step < len(pred_data.predicted_positions):
                    pred_x, pred_y = pred_data.predicted_positions[prediction_step]
                    primary_region_id = face_assignments[primary_face_idx]
                    primary_region = next((r for r in regions if r.region_id == primary_region_id), None)

                    if primary_region:
                        crop_x = int(pred_x - primary_region.center_x)
                        crop_y = int(pred_y - primary_region.center_y)
                        return crop_x, crop_y

        return None

    def _calculate_layout_confidence_with_prediction(self, faces: List[FaceDetection],
                                                   regions: List[FaceRegion],
                                                   face_assignments: Dict[int, str],
                                                   predictive_data: List[PredictiveFaceData]) -> float:
        """
        Enhanced confidence calculation incorporating predictive factors

        Args:
            faces: Current faces
            regions: Layout regions
            face_assignments: Face assignments
            predictive_data: Predictive face data

        Returns:
            Enhanced confidence score
        """
        # Start with base confidence
        base_confidence = self._calculate_layout_confidence(faces, regions, face_assignments)

        if not predictive_data:
            return base_confidence

        # Calculate predictive confidence factors
        avg_stability = sum(p.stability_score for p in predictive_data) / len(predictive_data)

        # Bonus for high stability (more predictable layout)
        stability_bonus = min(0.2, avg_stability * 0.3)  # Up to 20% bonus

        # Penalty for high velocity (less stable layout)
        avg_velocity = sum(np.sqrt(p.velocity[0]**2 + p.velocity[1]**2) for p in predictive_data) / len(predictive_data)
        velocity_penalty = min(0.15, avg_velocity / 100)  # Up to 15% penalty for high velocity

        enhanced_confidence = base_confidence + stability_bonus - velocity_penalty
        return max(0.0, min(1.0, enhanced_confidence))

    def _apply_predictive_layout_transition(self, new_layout: FacePositionLayout,
                                          timestamp: float) -> FacePositionLayout:
        """
        Enhanced layout transition using predictive awareness

        Args:
            new_layout: New layout to transition to
            timestamp: Current timestamp

        Returns:
            Layout with predictive transition applied
        """
        if not self.previous_layout:
            return new_layout

        # Check if the transition is predicted by segment plans
        transition_predicted = (new_layout.segment_plan and
                              new_layout.segment_plan.transition_required)

        if transition_predicted:
            # Use faster transition for predicted changes
            self.max_transition_frames = 6  # Faster transition
        else:
            # Use standard transition for unexpected changes
            self.max_transition_frames = 10

        # Apply standard transition logic with adjusted parameters
        return self._apply_layout_transition(new_layout, timestamp)
