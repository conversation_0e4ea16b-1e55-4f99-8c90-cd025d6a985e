#!/usr/bin/env python3
"""
Configuration settings for the reframing package

This module provides centralized configuration management for the reframing package,
allowing for easy customization of face detection, tracking, and crop parameters.
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class ReframingConfig:
    """Configuration class for reframing parameters"""

    # Face Detection Settings - ADVANCED CONFIGURATION
    face_detection_enabled: bool = True
    face_detection_backend: str = "mediapipe"  # MediaPipe provides excellent results for face detection
    face_detection_confidence: float = 0.3  # Lower threshold for better detection (enhanced filtering applied later)
    face_detection_gpu_enabled: bool = True  # Enable GPU acceleration for face detection

    # Face Tracking Settings - OPTIMIZED FOR CENTERING ACCURACY
    face_tracking_smoothing: float = 0.2  # Reduced smoothing for better centering precision
    face_crop_margin: float = 0.15  # Optimized margin for better composition

    # Speaker Identification Settings
    speaker_identification_enabled: bool = True

    # Motion and Fallback Settings
    motion_fallback_enabled: bool = True
    crop_transition_duration: float = 1.0  # Seconds for smooth transitions

    # Video Output Settings
    video_width: int = 720  # Target width for vertical video
    video_height: int = 1280  # Target height for vertical video

    # Encoding Settings
    video_codec: str = "libx264"
    audio_codec: str = "aac"
    encoding_preset: str = "fast"
    crf: int = 23  # Constant Rate Factor (quality)

    @classmethod
    def from_env(cls) -> 'ReframingConfig':
        """Create configuration from environment variables"""
        return cls(
            face_detection_enabled=os.getenv("FACE_DETECTION_ENABLED", "True").lower() == "true",
            face_detection_backend=os.getenv("FACE_DETECTION_BACKEND", "mediapipe"),
            face_detection_confidence=float(os.getenv("FACE_DETECTION_CONFIDENCE", "0.5")),
            face_detection_gpu_enabled=os.getenv("FACE_DETECTION_GPU_ENABLED", "True").lower() == "true",
            face_tracking_smoothing=float(os.getenv("FACE_TRACKING_SMOOTHING", "0.4")),
            face_crop_margin=float(os.getenv("FACE_CROP_MARGIN", "0.2")),
            speaker_identification_enabled=os.getenv("SPEAKER_IDENTIFICATION_ENABLED", "True").lower() == "true",
            motion_fallback_enabled=os.getenv("MOTION_FALLBACK_ENABLED", "True").lower() == "true",
            crop_transition_duration=float(os.getenv("CROP_TRANSITION_DURATION", "1.0")),
            video_width=int(os.getenv("VIDEO_WIDTH", "720")),
            video_height=int(os.getenv("VIDEO_HEIGHT", "1280")),
            video_codec=os.getenv("VIDEO_CODEC", "libx264"),
            audio_codec=os.getenv("AUDIO_CODEC", "aac"),
            encoding_preset=os.getenv("ENCODING_PRESET", "fast"),
            crf=int(os.getenv("CRF", "23"))
        )

    def validate(self) -> bool:
        """Validate configuration parameters"""
        if not 0 <= self.face_detection_confidence <= 1:
            raise ValueError("face_detection_confidence must be between 0 and 1")

        if not 0 <= self.face_tracking_smoothing <= 1:
            raise ValueError("face_tracking_smoothing must be between 0 and 1")

        if not 0 <= self.face_crop_margin <= 1:
            raise ValueError("face_crop_margin must be between 0 and 1")

        if self.face_detection_backend not in ["mediapipe", "insightface"]:
            raise ValueError("Invalid face_detection_backend")

        if self.video_width <= 0 or self.video_height <= 0:
            raise ValueError("Video dimensions must be positive")

        if not 1 <= self.crf <= 51:
            raise ValueError("CRF must be between 1 and 51")

        return True


# Default configuration instance
default_config = ReframingConfig.from_env()
