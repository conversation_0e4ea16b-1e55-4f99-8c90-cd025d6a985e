#!/usr/bin/env python3
"""
Intelligent face detection engine with multiple backend support

This module provides a unified interface for face detection using MediaPipe and InsightFace backends.
"""

import numpy as np
import logging
from typing import List, Optional

from ..models.data_classes import FaceDetection, GroupFaceBounds
from .backends import (
    MEDIAPIPE_AVAILABLE,
    INSIGHTFACE_AVAILABLE
)


class FaceDetectionEngine:
    """
    Intelligent face detection engine with multiple backend support
    """

    def __init__(self, backend: str = "auto", confidence_threshold: float = 0.5, enable_gpu: bool = True):
        self.backend = backend
        self.confidence_threshold = confidence_threshold
        self.enable_gpu = enable_gpu
        self.detector = None
        self.detector_full_range = None
        self.gpu_available = False
        self.using_gpu = False
        # Add logger for debugging
        self.logger = logging.getLogger(__name__)

        # Detect GPU availability
        self._detect_gpu_availability()

        # Initialize detector
        self._initialize_detector()

    def _detect_gpu_availability(self):
        """Detect GPU availability for MediaPipe and other backends"""
        try:
            # Check for CUDA availability (for InsightFace and general GPU support)
            try:
                import torch
                cuda_available = torch.cuda.is_available()
                if cuda_available:
                    self.logger.info(f"CUDA detected: {torch.cuda.get_device_name(0)}")
                else:
                    self.logger.info("CUDA not available")
            except ImportError:
                cuda_available = False
                self.logger.debug("PyTorch not available for CUDA detection")

            # Check for OpenGL/EGL support (for MediaPipe GPU)
            opengl_available = False
            try:
                import os
                # Check if we're in a headless environment
                display = os.environ.get('DISPLAY')
                if display or os.environ.get('EGL_PLATFORM'):
                    opengl_available = True
                    self.logger.debug("Display/EGL environment detected")
                else:
                    self.logger.debug("Headless environment detected")
            except Exception as e:
                self.logger.debug(f"OpenGL detection failed: {e}")

            # Determine overall GPU availability
            self.gpu_available = (cuda_available or opengl_available) and self.enable_gpu

            if self.gpu_available:
                self.logger.info("GPU acceleration available and enabled")
            else:
                self.logger.info("GPU acceleration not available or disabled, using CPU")

        except Exception as e:
            self.logger.warning(f"GPU detection failed: {e}, defaulting to CPU")
            self.gpu_available = False

    def _configure_mediapipe_environment(self):
        """Configure MediaPipe environment variables for optimal GPU/CPU performance"""
        import os

        # Basic logging configuration
        os.environ.setdefault('GLOG_minloglevel', '2')
        os.environ.setdefault('TF_CPP_MIN_LOG_LEVEL', '2')

        if self.gpu_available and self.enable_gpu:
            # GPU acceleration configuration
            self.logger.info("Configuring MediaPipe for GPU acceleration")

            # Enable GPU delegate
            os.environ.setdefault('MEDIAPIPE_DISABLE_GPU', '0')

            # OpenGL/EGL configuration for headless environments
            if not os.environ.get('DISPLAY'):
                # Headless environment - configure EGL
                os.environ.setdefault('EGL_PLATFORM', 'surfaceless')
                os.environ.setdefault('MESA_GL_VERSION_OVERRIDE', '3.3')
                os.environ.setdefault('MESA_GLSL_VERSION_OVERRIDE', '330')
                self.logger.debug("Configured EGL for headless GPU acceleration")

            # GPU memory and performance settings
            os.environ.setdefault('TF_FORCE_GPU_ALLOW_GROWTH', 'true')
            os.environ.setdefault('TF_GPU_THREAD_MODE', 'gpu_private')

        else:
            # CPU-only configuration
            self.logger.info("Configuring MediaPipe for CPU-only execution")
            os.environ['MEDIAPIPE_DISABLE_GPU'] = '1'

            # CPU optimization settings
            os.environ.setdefault('TF_NUM_INTEROP_THREADS', '0')  # Use all available cores
            os.environ.setdefault('TF_NUM_INTRAOP_THREADS', '0')  # Use all available cores

    def _initialize_detector(self):
        """Initialize the face detection backend"""
        if self.backend == "auto":
            # Auto-select best available backend
            self._auto_select_backend()
        elif self.backend == "insightface" and INSIGHTFACE_AVAILABLE:
            self._initialize_insightface_detector()
        elif self.backend == "mediapipe" and MEDIAPIPE_AVAILABLE:
            self._initialize_mediapipe_detector()
        else:
            # Fallback to available backends in order of preference
            self._fallback_to_available_backend()



    def _initialize_mediapipe_detector(self):
        """Initialize MediaPipe face detector with GPU acceleration support"""
        try:
            import os
            import signal
            import threading
            import time

            # Configure MediaPipe environment variables
            self._configure_mediapipe_environment()

            # Import MediaPipe
            import mediapipe as mp

            self.mp_face_detection = mp.solutions.face_detection
            # Try to import face mesh for facial landmarks (optional for ultra-precision)
            try:
                self.mp_face_mesh = mp.solutions.face_mesh
                self.has_face_mesh = True
            except:
                self.mp_face_mesh = None
                self.has_face_mesh = False

            # Initialize multiple detectors for ultra-precise detection
            detector_result = [None, None, None]  # [short_range, full_range, face_mesh]
            exception_result = [None]

            def init_detector():
                try:
                    # Advanced configuration: Use very low confidence for maximum detection
                    # We'll filter results later based on quality
                    min_confidence = min(0.2, self.confidence_threshold * 0.5)

                    # Try GPU initialization first if available
                    if self.gpu_available and self.enable_gpu:
                        try:
                            self.logger.debug("Attempting GPU-accelerated MediaPipe initialization")

                            # Short-range detector for close faces (most common in videos)
                            detector_result[0] = self.mp_face_detection.FaceDetection(
                                model_selection=0,  # Short-range model (2 meters)
                                min_detection_confidence=min_confidence
                            )

                            # Full-range detector for distant faces
                            detector_result[1] = self.mp_face_detection.FaceDetection(
                                model_selection=1,  # Full-range model (5 meters)
                                min_detection_confidence=min_confidence
                            )

                            # Test GPU initialization with a dummy operation
                            self._test_gpu_initialization()
                            self.using_gpu = True
                            self.logger.info("Successfully initialized MediaPipe with GPU acceleration")

                        except Exception as gpu_error:
                            self.logger.warning(f"GPU initialization failed: {gpu_error}, falling back to CPU")
                            # Clear any partially initialized detectors
                            detector_result[0] = None
                            detector_result[1] = None
                            # Fall through to CPU initialization

                    # CPU initialization (either as fallback or primary)
                    if detector_result[0] is None:
                        self.logger.debug("Initializing MediaPipe with CPU")
                        # Force CPU mode
                        import os
                        os.environ['MEDIAPIPE_DISABLE_GPU'] = '1'

                        # Reinitialize MediaPipe solutions with CPU
                        import mediapipe as mp
                        # Force reload to pick up environment changes
                        import importlib
                        importlib.reload(mp.solutions.face_detection)
                        self.mp_face_detection = mp.solutions.face_detection

                        # Short-range detector for close faces (most common in videos)
                        detector_result[0] = self.mp_face_detection.FaceDetection(
                            model_selection=0,  # Short-range model (2 meters)
                            min_detection_confidence=min_confidence
                        )

                        # Full-range detector for distant faces
                        detector_result[1] = self.mp_face_detection.FaceDetection(
                            model_selection=1,  # Full-range model (5 meters)
                            min_detection_confidence=min_confidence
                        )

                        self.using_gpu = False
                        self.logger.info("Successfully initialized MediaPipe with CPU")

                except Exception as e:
                    exception_result[0] = e

            # Run initialization in a separate thread with timeout
            init_thread = threading.Thread(target=init_detector)
            init_thread.daemon = True
            init_thread.start()
            init_thread.join(timeout=15.0)  # Increased timeout for dual initialization

            if init_thread.is_alive():
                self.logger.warning("MediaPipe initialization timed out after 15 seconds")
                self.detector = None
                self.detector_full_range = None
                return

            if exception_result[0]:
                raise exception_result[0]

            if detector_result[0] is None:
                raise Exception("MediaPipe detector initialization returned None")

            # Store both detectors for advanced detection
            self.detector = detector_result[0]  # Primary short-range detector
            self.detector_full_range = detector_result[1]  # Secondary full-range detector

            # Log final configuration
            gpu_status = "GPU" if self.using_gpu else "CPU"
            self.logger.info(f"Successfully initialized MediaPipe face detectors ({gpu_status}) - short-range + full-range")

        except Exception as e:
            self.logger.warning(f"Failed to initialize MediaPipe face detector: {e}")
            self.detector = None
            self.detector_full_range = None
            self.using_gpu = False

    def _test_gpu_initialization(self):
        """Test GPU initialization with a minimal operation"""
        try:
            # Create a small test image to verify GPU pipeline works
            import numpy as np
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)

            # Try processing with the detector
            if self.detector:
                results = self.detector.process(test_image)
                # If we get here without exception, GPU is working
                self.logger.debug("GPU test successful")
            else:
                raise Exception("Detector not initialized")

        except Exception as e:
            raise Exception(f"GPU test failed: {e}")



    def _initialize_insightface_detector(self):
        """Initialize InsightFace SCRFD detector with GPU acceleration support"""
        try:
            import insightface

            # Configure execution providers based on GPU availability
            if self.gpu_available and self.enable_gpu:
                try:
                    # Try GPU first
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                    self.detector = insightface.app.FaceAnalysis(providers=providers)
                    self.detector.prepare(ctx_id=0, det_size=(640, 640))

                    # Test GPU functionality
                    import numpy as np
                    test_image = np.zeros((100, 100, 3), dtype=np.uint8)
                    _ = self.detector.get(test_image)

                    self.using_gpu = True
                    self.logger.info("Initialized InsightFace SCRFD detector with GPU acceleration")

                except Exception as gpu_error:
                    self.logger.warning(f"InsightFace GPU initialization failed: {gpu_error}, falling back to CPU")
                    # Fall back to CPU
                    self.detector = insightface.app.FaceAnalysis(providers=['CPUExecutionProvider'])
                    self.detector.prepare(ctx_id=0, det_size=(640, 640))
                    self.using_gpu = False
                    self.logger.info("Initialized InsightFace SCRFD detector with CPU")
            else:
                # CPU only
                self.detector = insightface.app.FaceAnalysis(providers=['CPUExecutionProvider'])
                self.detector.prepare(ctx_id=0, det_size=(640, 640))
                self.using_gpu = False
                self.logger.info("Initialized InsightFace SCRFD detector with CPU")

        except Exception as e:
            self.logger.error(f"Failed to initialize InsightFace detector: {str(e)}")
            self.detector = None
            self.using_gpu = False

    def get_gpu_status(self) -> dict:
        """Get current GPU acceleration status"""
        return {
            'gpu_available': self.gpu_available,
            'gpu_enabled': self.enable_gpu,
            'using_gpu': self.using_gpu,
            'backend': self.backend,
            'detector_initialized': self.detector is not None
        }



    def _auto_select_backend(self):
        """Auto-select the best available backend in order of preference"""
        # Order of preference: MediaPipe > InsightFace
        # Note: MediaPipe first for best balance of accuracy and speed

        # Try MediaPipe first (best balance of accuracy and speed with good results)
        if MEDIAPIPE_AVAILABLE:
            self.logger.info("Attempting MediaPipe initialization (preferred for good results)...")
            self.backend = "mediapipe"
            self._initialize_mediapipe_detector()
            if self.detector is not None:
                return
            else:
                self.logger.warning("MediaPipe initialization failed, trying next backend")

        # Try InsightFace (high accuracy)
        if INSIGHTFACE_AVAILABLE:
            self.backend = "insightface"
            self._initialize_insightface_detector()
            if self.detector is not None:
                return

        # No backend available
        self.detector = None
        self.logger.warning("No face detection backend available")

    def _fallback_to_available_backend(self):
        """Fallback to available backends in order of preference"""
        if MEDIAPIPE_AVAILABLE:
            self.backend = "mediapipe"
            self._initialize_mediapipe_detector()
        elif INSIGHTFACE_AVAILABLE:
            self.backend = "insightface"
            self._initialize_insightface_detector()
        else:
            self.detector = None
            self.logger.warning("No face detection backend available, falling back to center crop")

    def detect_faces(self, frame: np.ndarray) -> List[FaceDetection]:
        """
        Detect faces in a frame using the configured backend

        Args:
            frame: Input frame as numpy array (BGR format)

        Returns:
            List of FaceDetection objects
        """
        if self.detector is None:
            return []

        faces = []
        height, width = frame.shape[:2]

        try:
            if self.backend == "insightface":
                faces = self._detect_faces_insightface(frame, width, height)
            elif self.backend == "mediapipe":
                faces = self._detect_faces_mediapipe(frame, width, height)

        except Exception as e:
            # Log error but don't crash
            self.logger.error(f"Face detection error with {self.backend}: {str(e)}")

        return faces

    def _detect_faces_insightface(self, frame: np.ndarray, width: int, height: int) -> List[FaceDetection]:
        """Detect faces using InsightFace SCRFD (best accuracy)"""
        faces = []

        try:
            # Import cv2 only when needed
            import cv2

            # InsightFace expects RGB format
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Get face detections
            detections = self.detector.get(rgb_frame)

            for detection in detections:
                # InsightFace returns bbox as [x1, y1, x2, y2]
                bbox = detection.bbox.astype(int)
                x, y, x2, y2 = bbox
                w = x2 - x
                h = y2 - y

                # InsightFace provides detection score
                confidence = float(detection.det_score)

                if confidence >= self.confidence_threshold:
                    faces.append(FaceDetection(x, y, w, h, confidence, 0, 0))

        except Exception as e:
            self.logger.error(f"InsightFace detection error: {e}")

        return faces





    def _detect_faces_mediapipe(self, frame: np.ndarray, width: int, height: int) -> List[FaceDetection]:
        """WORLD-CLASS MediaPipe face detection with dual-model approach"""
        faces = []

        # Import cv2 only when needed
        import cv2

        # Convert BGR to RGB for MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # World-class approach: Use both short-range and full-range detectors
        all_detections = []

        # Primary detection with short-range model (best for close faces)
        if self.detector:
            try:
                results = self.detector.process(rgb_frame)
                if results.detections:
                    for detection in results.detections:
                        bbox = detection.location_data.relative_bounding_box
                        confidence = detection.score[0]

                        # ULTRA-HIGH PRECISION coordinate conversion for world-class face tracking
                        # Use double precision floating point for maximum accuracy
                        x_precise = float(bbox.xmin) * float(width)
                        y_precise = float(bbox.ymin) * float(height)
                        w_precise = float(bbox.width) * float(width)
                        h_precise = float(bbox.height) * float(height)

                        # Early size validation - reject tiny false positives immediately
                        min_face_size = max(40, min(width, height) * 0.03)  # At least 40px or 3% of frame dimension
                        if w_precise < min_face_size or h_precise < min_face_size:
                            self.logger.debug(f"Rejecting tiny detection: {w_precise:.1f}x{h_precise:.1f} < {min_face_size}")
                            continue  # Skip this detection entirely

                        # Enhanced confidence calculation with quality assessment
                        enhanced_confidence = self._calculate_enhanced_confidence(
                            bbox, confidence, width, height, "short_range"
                        )

                        if enhanced_confidence >= self.confidence_threshold:  # Use actual threshold, no artificial lowering
                            # Calculate ultra-precise center coordinates before rounding
                            center_x_precise = x_precise + w_precise / 2.0
                            center_y_precise = y_precise + h_precise / 2.0

                            # Round to nearest pixel for bounding box
                            x = max(0, round(x_precise))
                            y = max(0, round(y_precise))
                            w = min(width - x, round(w_precise))
                            h = min(height - y, round(h_precise))

                            # Final validation after rounding
                            if w >= min_face_size and h >= min_face_size:  # Realistic minimum face size
                                all_detections.append({
                                    'bbox': (x, y, w, h),
                                    'confidence': enhanced_confidence,
                                    'source': 'short_range',
                                    'precise_center': (center_x_precise, center_y_precise)  # Store ultra-precise center
                                })
            except Exception as e:
                self.logger.debug(f"Short-range MediaPipe detection failed: {e}")

        # Secondary detection with full-range model (best for distant faces)
        if hasattr(self, 'detector_full_range') and self.detector_full_range:
            try:
                results_full = self.detector_full_range.process(rgb_frame)
                if results_full.detections:
                    for detection in results_full.detections:
                        bbox = detection.location_data.relative_bounding_box
                        confidence = detection.score[0]

                        # ULTRA-HIGH PRECISION coordinate conversion for world-class face tracking
                        # Use double precision floating point for maximum accuracy
                        x_precise = float(bbox.xmin) * float(width)
                        y_precise = float(bbox.ymin) * float(height)
                        w_precise = float(bbox.width) * float(width)
                        h_precise = float(bbox.height) * float(height)

                        # Early size validation - reject tiny false positives immediately
                        min_face_size = max(40, min(width, height) * 0.03)  # At least 40px or 3% of frame dimension
                        if w_precise < min_face_size or h_precise < min_face_size:
                            self.logger.debug(f"Rejecting tiny detection (full-range): {w_precise:.1f}x{h_precise:.1f} < {min_face_size}")
                            continue  # Skip this detection entirely

                        # Enhanced confidence calculation
                        enhanced_confidence = self._calculate_enhanced_confidence(
                            bbox, confidence, width, height, "full_range"
                        )

                        if enhanced_confidence >= self.confidence_threshold:  # Use actual threshold, no artificial lowering
                            # Calculate ultra-precise center coordinates before rounding
                            center_x_precise = x_precise + w_precise / 2.0
                            center_y_precise = y_precise + h_precise / 2.0

                            # Round to nearest pixel for bounding box
                            x = max(0, round(x_precise))
                            y = max(0, round(y_precise))
                            w = min(width - x, round(w_precise))
                            h = min(height - y, round(h_precise))

                            # Final validation after rounding
                            if w >= min_face_size and h >= min_face_size:  # Realistic minimum face size
                                all_detections.append({
                                    'bbox': (x, y, w, h),
                                    'confidence': enhanced_confidence,
                                    'source': 'full_range',
                                    'precise_center': (center_x_precise, center_y_precise)  # Store ultra-precise center
                                })
            except Exception as e:
                self.logger.debug(f"Full-range MediaPipe detection failed: {e}")

        # Merge and deduplicate detections
        merged_detections = self._merge_duplicate_detections(all_detections)

        # Convert to FaceDetection objects with ultra-high precision centers
        for detection in merged_detections:
            x, y, w, h = detection['bbox']
            confidence = detection['confidence']

            # Create FaceDetection with ultra-precise center coordinates
            face_detection = FaceDetection(x, y, w, h, confidence, 0, 0)

            # Apply ultra-precise center coordinates if available
            if 'precise_center' in detection:
                precise_center_x, precise_center_y = detection['precise_center']
                face_detection.precise_center_x = precise_center_x
                face_detection.precise_center_y = precise_center_y
                # Update the main center coordinates with ultra-precise values
                face_detection.center_x = precise_center_x
                face_detection.center_y = precise_center_y

            faces.append(face_detection)

        return faces

    def _calculate_enhanced_confidence(self, bbox, base_confidence: float, width: int, height: int, source: str) -> float:
        """Calculate enhanced confidence score with strict quality assessment to reject false positives"""
        try:
            # Face size assessment
            face_width = bbox.width * width
            face_height = bbox.height * height
            face_area = face_width * face_height
            frame_area = width * height

            # Size check is now done earlier in the detection pipeline
            # This function focuses on quality assessment of valid-sized faces

            # STRICT quality checks to reject wall tiles, patterns, etc.

            # 1. Optimal face size ratio (stricter range)
            size_ratio = face_area / frame_area
            if 0.03 <= size_ratio <= 0.12:  # Stricter range for realistic faces
                size_bonus = 0.02
            elif 0.02 <= size_ratio <= 0.20:
                size_bonus = 0.0  # No bonus for edge cases
            else:
                size_bonus = -0.2  # Heavy penalty for unrealistic sizes

            # 2. Strict aspect ratio (faces should be nearly square)
            aspect_ratio = face_width / face_height if face_height > 0 else 0
            if 0.8 <= aspect_ratio <= 1.2:  # Stricter range
                aspect_bonus = 0.02
            elif 0.7 <= aspect_ratio <= 1.4:
                aspect_bonus = 0.0  # No bonus for edge cases
            else:
                aspect_bonus = -0.15  # Heavy penalty for non-face ratios

            # 3. Position assessment - faces shouldn't be at extreme edges
            face_center_x = bbox.xmin + bbox.width / 2
            face_center_y = bbox.ymin + bbox.height / 2

            # Penalize detections at extreme edges (likely false positives)
            if face_center_x < 0.1 or face_center_x > 0.9 or face_center_y < 0.1 or face_center_y > 0.9:
                edge_penalty = -0.1
            else:
                edge_penalty = 0.0

            # 4. Base confidence must be reasonably high for MediaPipe
            if base_confidence < 0.6:
                confidence_penalty = -0.2  # Heavy penalty for low base confidence
            elif base_confidence < 0.8:
                confidence_penalty = -0.1  # Moderate penalty
            else:
                confidence_penalty = 0.0

            # 5. Source-specific assessment (minimal bonus)
            source_bonus = 0.005 if source == "short_range" else 0.002

            enhanced_confidence = base_confidence + size_bonus + aspect_bonus + edge_penalty + confidence_penalty + source_bonus

            # Apply strict minimum threshold - reject anything that doesn't look like a real face
            final_confidence = max(0.0, min(1.0, enhanced_confidence))

            # Additional strict filter: if enhanced confidence is too low, reject entirely
            if final_confidence < 0.5:
                self.logger.debug(f"Rejecting low-quality detection: base={base_confidence:.3f}, final={final_confidence:.3f}")
                return 0.0

            return final_confidence

        except Exception:
            return base_confidence

    def _merge_duplicate_detections(self, detections: list) -> list:
        """Merge duplicate face detections using Non-Maximum Suppression"""
        if len(detections) <= 1:
            return detections

        # Sort by confidence (highest first)
        detections.sort(key=lambda x: x['confidence'], reverse=True)

        merged = []
        for detection in detections:
            x1, y1, w1, h1 = detection['bbox']
            is_duplicate = False

            for existing in merged:
                x2, y2, w2, h2 = existing['bbox']

                # Calculate IoU (Intersection over Union)
                iou = self._calculate_iou((x1, y1, w1, h1), (x2, y2, w2, h2))

                # If IoU > 0.3, consider it a duplicate
                if iou > 0.3:
                    is_duplicate = True
                    # Keep the one with higher confidence (already sorted)
                    if detection['confidence'] > existing['confidence']:
                        # Replace existing with current (higher confidence)
                        existing.update(detection)
                    break

            if not is_duplicate:
                merged.append(detection)

        return merged

    def _calculate_iou(self, box1: tuple, box2: tuple) -> float:
        """Calculate Intersection over Union (IoU) of two bounding boxes"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2

        # Calculate intersection
        xi1 = max(x1, x2)
        yi1 = max(y1, y2)
        xi2 = min(x1 + w1, x2 + w2)
        yi2 = min(y1 + h1, y2 + h2)

        if xi2 <= xi1 or yi2 <= yi1:
            return 0.0

        intersection = (xi2 - xi1) * (yi2 - yi1)
        union = w1 * h1 + w2 * h2 - intersection

        return intersection / union if union > 0 else 0.0



    def _remove_duplicate_detections(self, detections):
        """Remove duplicate face detections that overlap significantly"""
        if not detections:
            return []

        # Convert to list if it's a numpy array
        detections = list(detections)

        # Sort by area (largest first) to prioritize larger detections
        detections.sort(key=lambda d: d[2] * d[3], reverse=True)

        unique_detections = []
        for detection in detections:
            x1, y1, w1, h1 = detection

            # Check if this detection overlaps significantly with any existing unique detection
            is_duplicate = False
            for unique_detection in unique_detections:
                x2, y2, w2, h2 = unique_detection

                # Calculate overlap
                overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
                overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
                overlap_area = overlap_x * overlap_y

                area1 = w1 * h1
                area2 = w2 * h2
                min_area = min(area1, area2)

                # If overlap is more than 50% of the smaller detection, consider it a duplicate
                if overlap_area > 0.5 * min_area:
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_detections.append(detection)

        return unique_detections

    def calculate_group_face_bounds(self, faces: List[FaceDetection], frame_width: int, frame_height: int) -> Optional[GroupFaceBounds]:
        """
        Calculate bounding box that encompasses all detected faces for group tracking

        Args:
            faces: List of detected faces
            frame_width: Frame width
            frame_height: Frame height

        Returns:
            GroupFaceBounds object or None if no faces
        """
        if not faces:
            return None

        if len(faces) == 1:
            # Single face - use face bounds with some padding
            face = faces[0]
            padding = max(face.width, face.height) // 4  # 25% padding
            x = max(0, face.x - padding)
            y = max(0, face.y - padding)
            width = min(frame_width - x, face.width + 2 * padding)
            height = min(frame_height - y, face.height + 2 * padding)

            return GroupFaceBounds(x, y, width, height, 0, 0, 1, face.confidence)

        # Multiple faces - calculate encompassing bounding box
        min_x = min(face.x for face in faces)
        min_y = min(face.y for face in faces)
        max_x = max(face.x + face.width for face in faces)
        max_y = max(face.y + face.height for face in faces)

        # Enhanced padding logic for optimal two-face scenarios
        group_width = max_x - min_x
        group_height = max_y - min_y

        if len(faces) == 2:
            # Advanced two-face handling with intelligent padding calculation
            face_centers_x = [face.center_x for face in faces]
            face_centers_y = [face.center_y for face in faces]
            horizontal_separation = max(face_centers_x) - min(face_centers_x)
            vertical_separation = max(face_centers_y) - min(face_centers_y)

            # Calculate face sizes for better padding decisions
            face_widths = [face.width for face in faces]
            face_heights = [face.height for face in faces]
            avg_face_width = sum(face_widths) / len(face_widths)
            avg_face_height = sum(face_heights) / len(face_heights)

            # Determine if faces are in left-right configuration (horizontal separation)
            is_horizontal_layout = horizontal_separation > vertical_separation

            if is_horizontal_layout and horizontal_separation > frame_width * 0.25:
                # Wide horizontal separation - optimize for vertical crop inclusion
                # Use minimal horizontal padding but ensure safety margins
                base_padding = max(15, avg_face_width * 0.1)  # 10% of average face width, min 15px

                # Dynamic padding based on separation distance
                separation_ratio = horizontal_separation / frame_width
                if separation_ratio > 0.5:  # Very wide separation
                    padding_x = max(base_padding, int(avg_face_width * 0.05))  # Minimal padding
                    self.logger.debug(f"Very wide face separation ({horizontal_separation}px, ratio: {separation_ratio:.2f}), "
                                    f"using minimal padding: {padding_x}px")
                elif separation_ratio > 0.35:  # Moderate separation
                    padding_x = max(base_padding, int(avg_face_width * 0.08))
                    self.logger.debug(f"Moderate face separation ({horizontal_separation}px, ratio: {separation_ratio:.2f}), "
                                    f"using conservative padding: {padding_x}px")
                else:  # Manageable separation
                    padding_x = max(base_padding, int(avg_face_width * 0.12))
                    self.logger.debug(f"Manageable face separation ({horizontal_separation}px, ratio: {separation_ratio:.2f}), "
                                    f"using standard padding: {padding_x}px")

                # Vertical padding can be more generous for horizontal layouts
                padding_y = max(int(avg_face_height * 0.15), group_height // 8)

            else:
                # Faces are closer together or in vertical layout, use standard padding
                padding_x = max(20, group_width // 6)  # 16% padding horizontally
                padding_y = max(15, group_height // 8)  # 12% padding vertically
                self.logger.debug(f"Close or vertical face layout, using standard padding: x={padding_x}px, y={padding_y}px")

        else:
            # For other face counts, use standard padding
            padding_x = group_width // 6  # 16% padding horizontally
            padding_y = group_height // 8  # 12% padding vertically

        # Apply padding with bounds checking
        x = max(0, min_x - padding_x)
        y = max(0, min_y - padding_y)
        width = min(frame_width - x, group_width + 2 * padding_x)
        height = min(frame_height - y, group_height + 2 * padding_y)

        # Calculate average confidence
        avg_confidence = sum(face.confidence for face in faces) / len(faces)

        return GroupFaceBounds(int(x), int(y), int(width), int(height), 0, 0, len(faces), avg_confidence)
