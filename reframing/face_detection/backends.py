#!/usr/bin/env python3
"""
Backend availability checks for face detection libraries
"""

import logging
logger = logging.getLogger(__name__)

# Check InsightFace SCRFD availability (high accuracy)
INSIGHTFACE_AVAILABLE = False
try:
    import insightface
    INSIGHTFACE_AVAILABLE = True
    logger.info("InsightFace SCRFD backend available")
except ImportError:
    logger.warning("InsightFace not available - install with: pip install insightface")

# Check MediaPipe availability
MEDIAPIPE_AVAILABLE = False
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    logger.info("MediaPipe backend available")
except ImportError:
    logger.warning("MediaPipe not available - install with: pip install mediapipe")
