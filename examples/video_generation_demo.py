#!/usr/bin/env python3
"""
Video Generation Demonstration Script

This script demonstrates how to use the comprehensive video test generator
to create validation videos for the enhanced face positioning system.
"""

import sys
import os
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tools.video_test_generator import VideoTestGenerator, TestFace, VideoScenario

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demonstrate_single_scenario():
    """Demonstrate generating a single test scenario"""
    logger.info("🎬 Demonstrating Single Scenario Generation")
    logger.info("=" * 50)
    
    # Create output directory
    output_dir = "demo_videos"
    generator = VideoTestGenerator(output_dir=output_dir)
    
    # Create a custom scenario for demonstration
    demo_faces = [
        # Face that moves from left to right
        TestFace(
            id=1,
            start_time=0.0,
            end_time=5.0,
            start_pos=(200, 400),
            end_pos=(1600, 400),
            size=(200, 200),
            confidence=0.92,
            movement_type='linear',
            color=(255, 100, 100)
        ),
        # Face that appears later and moves in a circle
        TestFace(
            id=2,
            start_time=2.0,
            end_time=5.0,
            start_pos=(800, 600),
            end_pos=(1200, 600),
            size=(180, 180),
            confidence=0.88,
            movement_type='circular',
            color=(100, 255, 100)
        )
    ]
    
    demo_scenario = VideoScenario(
        name="demo_scenario",
        duration=5.0,
        description="Custom demonstration scenario with two moving faces",
        faces=demo_faces,
        target_features=["custom_demo", "face_movement", "layout_transitions"]
    )
    
    # Generate the video
    logger.info(f"📹 Generating demo scenario video...")
    video_path = generator._generate_scenario_video(demo_scenario)
    
    # Generate comparison video
    logger.info(f"🔄 Generating comparison video...")
    generator._generate_comparison_videos({"demo_scenario": video_path})
    
    logger.info(f"✅ Demo scenario generated successfully!")
    logger.info(f"   Video: {video_path}")
    logger.info(f"   Comparison: {video_path.replace('.mp4', '_comparison.mp4')}")
    logger.info(f"   Face data: {video_path.replace('.mp4', '_faces.json')}")


def demonstrate_all_scenarios():
    """Demonstrate generating all test scenarios"""
    logger.info("\n🎬 Demonstrating All Scenarios Generation")
    logger.info("=" * 50)
    
    # Create output directory
    output_dir = "all_test_videos"
    generator = VideoTestGenerator(output_dir=output_dir)
    
    # Generate all scenarios
    logger.info("📹 Generating all test scenarios...")
    generated_videos = generator.generate_all_test_videos()
    
    # Generate performance report
    logger.info("📊 Generating performance report...")
    report_path = generator.generate_performance_report(generated_videos)
    
    logger.info(f"✅ All scenarios generated successfully!")
    logger.info(f"📁 Output directory: {output_dir}")
    logger.info(f"🎬 Videos generated: {len(generated_videos)}")
    logger.info(f"📊 Performance report: {report_path}")
    
    # List generated files
    logger.info("\n📹 Generated Videos:")
    for scenario_name, video_path in generated_videos.items():
        logger.info(f"   • {scenario_name}:")
        logger.info(f"     Original: {video_path}")
        logger.info(f"     Comparison: {video_path.replace('.mp4', '_comparison.mp4')}")
        logger.info(f"     Data: {video_path.replace('.mp4', '_faces.json')}")


def demonstrate_scenario_analysis():
    """Demonstrate analyzing generated scenarios"""
    logger.info("\n📊 Demonstrating Scenario Analysis")
    logger.info("=" * 40)
    
    generator = VideoTestGenerator()
    scenarios = generator._create_test_scenarios()
    
    logger.info(f"📋 Available Test Scenarios: {len(scenarios)}")
    
    for scenario in scenarios:
        logger.info(f"\n🎯 Scenario: {scenario.name}")
        logger.info(f"   Duration: {scenario.duration}s")
        logger.info(f"   Description: {scenario.description}")
        logger.info(f"   Faces: {len(scenario.faces)}")
        logger.info(f"   Target Features: {', '.join(scenario.target_features)}")
        
        # Analyze face movements
        movement_types = set(face.movement_type for face in scenario.faces)
        logger.info(f"   Movement Types: {', '.join(movement_types)}")
        
        # Analyze timing
        total_face_time = sum(face.end_time - face.start_time for face in scenario.faces)
        avg_face_duration = total_face_time / len(scenario.faces) if scenario.faces else 0
        logger.info(f"   Avg Face Duration: {avg_face_duration:.1f}s")
        
        # Analyze face count over time
        max_concurrent = 0
        for t in [i * 0.1 for i in range(int(scenario.duration * 10))]:
            active_count = len([f for f in scenario.faces if f.start_time <= t <= f.end_time])
            max_concurrent = max(max_concurrent, active_count)
        logger.info(f"   Max Concurrent Faces: {max_concurrent}")


def demonstrate_custom_scenario_creation():
    """Demonstrate creating custom test scenarios"""
    logger.info("\n🛠️ Demonstrating Custom Scenario Creation")
    logger.info("=" * 45)
    
    logger.info("Creating a custom scenario to test specific features...")
    
    # Create a scenario that tests rapid face count changes
    rapid_change_faces = [
        # Single face phase (0-3s)
        TestFace(1, 0.0, 3.0, (960, 540), (960, 540), (220, 220), 0.95, 'stationary', (255, 120, 120)),
        
        # Dual face phase (3-6s) - second face enters
        TestFace(2, 3.0, 6.0, (1600, 300), (600, 300), (200, 200), 0.90, 'linear', (120, 255, 120)),
        
        # Triple face phase (6-9s) - third face enters
        TestFace(3, 6.0, 9.0, (1600, 800), (1200, 800), (180, 180), 0.85, 'linear', (120, 120, 255)),
        
        # Quad face phase (9-12s) - fourth face enters
        TestFace(4, 9.0, 12.0, (400, 800), (400, 800), (170, 170), 0.88, 'stationary', (255, 255, 120)),
        
        # Back to triple (12-15s) - fourth face exits
        # Face 4 ends at 12s, so faces 1,2,3 continue
        
        # Back to dual (15-18s) - third face exits
        TestFace(3, 6.0, 15.0, (1600, 800), (1200, 800), (180, 180), 0.85, 'linear', (120, 120, 255)),  # Override end time
        
        # Back to single (18-20s) - second face exits
        TestFace(2, 3.0, 18.0, (1600, 300), (600, 300), (200, 200), 0.90, 'linear', (120, 255, 120)),  # Override end time
        
        # Single face continues to end (20s)
        TestFace(1, 0.0, 20.0, (960, 540), (960, 540), (220, 220), 0.95, 'stationary', (255, 120, 120)),  # Override end time
    ]
    
    # Fix the overlapping faces by creating separate face instances
    custom_faces = [
        # Single face phase (0-3s)
        TestFace(1, 0.0, 20.0, (960, 540), (960, 540), (220, 220), 0.95, 'stationary', (255, 120, 120)),
        
        # Second face (3-18s)
        TestFace(2, 3.0, 18.0, (1600, 300), (600, 300), (200, 200), 0.90, 'linear', (120, 255, 120)),
        
        # Third face (6-15s)
        TestFace(3, 6.0, 15.0, (1600, 800), (1200, 800), (180, 180), 0.85, 'linear', (120, 120, 255)),
        
        # Fourth face (9-12s)
        TestFace(4, 9.0, 12.0, (400, 800), (400, 800), (170, 170), 0.88, 'stationary', (255, 255, 120)),
    ]
    
    custom_scenario = VideoScenario(
        name="rapid_layout_changes",
        duration=20.0,
        description="Tests rapid layout changes from 1→2→3→4→3→2→1 faces",
        faces=custom_faces,
        target_features=["rapid_transitions", "layout_stability", "assignment_consistency"]
    )
    
    logger.info(f"📋 Custom Scenario Created:")
    logger.info(f"   Name: {custom_scenario.name}")
    logger.info(f"   Duration: {custom_scenario.duration}s")
    logger.info(f"   Faces: {len(custom_scenario.faces)}")
    logger.info(f"   Features: {', '.join(custom_scenario.target_features)}")
    
    # Analyze face count over time
    logger.info(f"\n⏱️ Face Count Timeline:")
    for t in range(0, int(custom_scenario.duration) + 1, 2):
        active_faces = [f for f in custom_scenario.faces if f.start_time <= t <= f.end_time]
        logger.info(f"   {t:2d}s: {len(active_faces)} faces")
    
    # Generate the custom scenario
    output_dir = "custom_test_videos"
    generator = VideoTestGenerator(output_dir=output_dir)
    
    logger.info(f"\n📹 Generating custom scenario video...")
    video_path = generator._generate_scenario_video(custom_scenario)
    
    logger.info(f"✅ Custom scenario generated!")
    logger.info(f"   Video: {video_path}")
    logger.info(f"   Data: {video_path.replace('.mp4', '_faces.json')}")


def demonstrate_validation_workflow():
    """Demonstrate the complete validation workflow"""
    logger.info("\n🔍 Demonstrating Complete Validation Workflow")
    logger.info("=" * 50)
    
    logger.info("This workflow shows how to use generated videos for validation:")
    
    logger.info("\n1️⃣ Generate Test Videos")
    logger.info("   • Use VideoTestGenerator to create comprehensive test scenarios")
    logger.info("   • Each scenario targets specific features of the face positioning system")
    logger.info("   • Videos include visual overlays showing system behavior")
    
    logger.info("\n2️⃣ Visual Validation")
    logger.info("   • Review original videos to see face detection and layout visualization")
    logger.info("   • Check comparison videos to see original vs. cropped output")
    logger.info("   • Verify smooth transitions between layout changes")
    logger.info("   • Confirm face assignments remain consistent")
    
    logger.info("\n3️⃣ Data Analysis")
    logger.info("   • Examine JSON files containing frame-by-frame data")
    logger.info("   • Analyze confidence scores and stability metrics")
    logger.info("   • Review predictive tracking accuracy")
    logger.info("   • Check segment planning effectiveness")
    
    logger.info("\n4️⃣ Performance Evaluation")
    logger.info("   • Review performance report for quantitative metrics")
    logger.info("   • Check layout transition counts and timing")
    logger.info("   • Analyze face count distributions")
    logger.info("   • Evaluate average confidence and stability scores")
    
    logger.info("\n5️⃣ Edge Case Testing")
    logger.info("   • Create custom scenarios for specific edge cases")
    logger.info("   • Test boundary conditions and error handling")
    logger.info("   • Validate system behavior under stress conditions")
    
    logger.info("\n✅ Validation Complete")
    logger.info("   Use the generated videos and data to verify that the enhanced")
    logger.info("   face positioning system meets all requirements and performs")
    logger.info("   correctly across all supported scenarios.")


def main():
    """Main demonstration function"""
    logger.info("🎬 Enhanced Face Positioning Video Generation Demo")
    logger.info("=" * 60)
    logger.info("This demo shows how to generate comprehensive test videos")
    logger.info("for validating the enhanced face positioning system.")
    
    try:
        # Run all demonstrations
        demonstrate_single_scenario()
        demonstrate_scenario_analysis()
        demonstrate_custom_scenario_creation()
        demonstrate_validation_workflow()
        
        # Optionally generate all scenarios (commented out to save time)
        # demonstrate_all_scenarios()
        
        logger.info("\n" + "="*60)
        logger.info("🎉 VIDEO GENERATION DEMO COMPLETE")
        logger.info("="*60)
        logger.info("✅ All demonstrations completed successfully!")
        logger.info("📁 Check the generated video directories for output files")
        logger.info("🎯 Use these videos to validate your face positioning system")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
