#!/usr/bin/env python3
"""
Dual Video Compositing Demonstration

This script demonstrates the dual video compositing system for two-face scenarios,
showing how the enhanced face positioning system creates optimized dual outputs.
"""

import sys
import os
import logging
import json
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from reframing.video.dual_video_compositor import DualVideoCompositor
from reframing.video.enhanced_dual_positioning import EnhancedDualFacePositioning
from tools.video_test_generator import VideoTestGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demonstrate_dual_face_detection():
    """Demonstrate dual face scenario detection"""
    logger.info("🔍 Demonstrating Dual Face Scenario Detection")
    logger.info("=" * 50)
    
    # Create test face data sequences
    test_scenarios = [
        {
            "name": "Consistent Dual Faces",
            "description": "90% of frames have exactly 2 faces",
            "data": create_test_sequence(dual_ratio=0.9, total_frames=100)
        },
        {
            "name": "Mixed Scenario", 
            "description": "50% dual faces, 50% single face",
            "data": create_test_sequence(dual_ratio=0.5, total_frames=100)
        },
        {
            "name": "Predominantly Single",
            "description": "20% dual faces, 80% single face", 
            "data": create_test_sequence(dual_ratio=0.2, total_frames=100)
        }
    ]
    
    for scenario in test_scenarios:
        logger.info(f"\n📊 Testing: {scenario['name']}")
        logger.info(f"   Description: {scenario['description']}")
        
        is_dual_scenario = DualVideoCompositor.is_dual_face_scenario(scenario['data'])
        logger.info(f"   Dual scenario detected: {is_dual_scenario}")
        
        # Calculate actual statistics
        dual_frames = sum(1 for frame in scenario['data'] if len(frame.get('faces', [])) == 2)
        dual_ratio = dual_frames / len(scenario['data'])
        logger.info(f"   Actual dual ratio: {dual_ratio:.2f}")
        logger.info(f"   Total frames: {len(scenario['data'])}")


def demonstrate_enhanced_dual_positioning():
    """Demonstrate enhanced dual face positioning integration"""
    logger.info("\n🎯 Demonstrating Enhanced Dual Face Positioning")
    logger.info("=" * 55)
    
    enhanced_positioning = EnhancedDualFacePositioning()
    
    # Create test dual face sequence
    dual_sequence = create_consistent_dual_sequence(duration=5.0)
    
    # Get processing recommendations
    logger.info("📋 Analyzing face data sequence...")
    recommendations = enhanced_positioning.get_processing_recommendations(dual_sequence)
    
    logger.info(f"   Analysis Results:")
    logger.info(f"   • Total frames: {recommendations['analysis']['total_frames']}")
    logger.info(f"   • Dual face ratio: {recommendations['analysis']['dual_face_ratio']:.2f}")
    logger.info(f"   • Is dual scenario: {recommendations['analysis']['is_dual_face_scenario']}")
    logger.info(f"   • Recommendation: {recommendations['analysis']['recommendation']}")
    
    logger.info(f"\n   Processing Options:")
    for option, enabled in recommendations['processing_options'].items():
        status = "✅" if enabled else "❌"
        logger.info(f"   {status} {option.replace('_', ' ').title()}")
    
    logger.info(f"\n   Expected Benefits:")
    for benefit in recommendations['expected_benefits']:
        logger.info(f"   • {benefit}")
    
    # Test configuration
    logger.info(f"\n🔧 Testing Configuration Options:")
    
    # Default configuration
    debug_info = enhanced_positioning.get_debug_info()
    config = debug_info['enhanced_dual_positioning']
    logger.info(f"   Default threshold: {config['dual_face_threshold']}")
    logger.info(f"   Dual output enabled: {config['enable_dual_output']}")
    logger.info(f"   Preserve single output: {config['preserve_single_output']}")
    
    # Change configuration
    enhanced_positioning.configure_dual_output(
        enable=True, preserve_single=False, dual_threshold=0.8
    )
    logger.info(f"   Updated threshold to 0.8, disabled single output preservation")


def demonstrate_face_track_separation():
    """Demonstrate face track separation and processing"""
    logger.info("\n👥 Demonstrating Face Track Separation")
    logger.info("=" * 45)
    
    compositor = DualVideoCompositor()
    
    # Create test sequence with moving faces
    sequence = create_moving_faces_sequence()
    
    logger.info(f"📊 Processing {len(sequence)} frames with dual faces")
    
    # Separate face tracks
    left_track, right_track = compositor._separate_face_tracks(sequence)
    
    logger.info(f"\n📈 Face Track Analysis:")
    logger.info(f"   Left Track:")
    logger.info(f"   • Face ID: {left_track.face_id}")
    logger.info(f"   • Target region: {left_track.target_region}")
    logger.info(f"   • Detections: {len(left_track.face_detections)}")
    logger.info(f"   • Timestamps: {len(left_track.timestamps)}")
    
    logger.info(f"   Right Track:")
    logger.info(f"   • Face ID: {right_track.face_id}")
    logger.info(f"   • Target region: {right_track.target_region}")
    logger.info(f"   • Detections: {len(right_track.face_detections)}")
    logger.info(f"   • Timestamps: {len(right_track.timestamps)}")
    
    # Generate individual layouts
    logger.info(f"\n🎯 Generating individual face layouts...")
    compositor._generate_individual_layouts(left_track, right_track)
    
    logger.info(f"   Left track layouts: {len(left_track.layouts)}")
    logger.info(f"   Right track layouts: {len(right_track.layouts)}")
    
    # Calculate average confidence for each track
    left_confidence = compositor._calculate_average_confidence(left_track)
    right_confidence = compositor._calculate_average_confidence(right_track)
    
    logger.info(f"   Left track avg confidence: {left_confidence:.3f}")
    logger.info(f"   Right track avg confidence: {right_confidence:.3f}")
    
    # Extract predictive features
    left_features = compositor._extract_predictive_features(left_track)
    right_features = compositor._extract_predictive_features(right_track)
    
    logger.info(f"\n🔮 Predictive Features:")
    logger.info(f"   Left track stability: {left_features.get('stability_analysis', {}).get('average_stability', 0):.3f}")
    logger.info(f"   Right track stability: {right_features.get('stability_analysis', {}).get('average_stability', 0):.3f}")


def demonstrate_video_generation_with_dual_output():
    """Demonstrate video generation with dual output capability"""
    logger.info("\n🎬 Demonstrating Video Generation with Dual Output")
    logger.info("=" * 55)
    
    # Create video generator
    generator = VideoTestGenerator(output_dir="dual_demo_videos")
    
    # Generate the dual video compositing scenario
    scenarios = generator._create_test_scenarios()
    dual_scenario = next((s for s in scenarios if s.name == "dual_video_compositing"), None)
    
    if dual_scenario:
        logger.info(f"📹 Generating dual video compositing scenario...")
        logger.info(f"   Scenario: {dual_scenario.name}")
        logger.info(f"   Duration: {dual_scenario.duration}s")
        logger.info(f"   Faces: {len(dual_scenario.faces)}")
        logger.info(f"   Features: {', '.join(dual_scenario.target_features)}")
        
        # Generate the video
        video_path = generator._generate_scenario_video(dual_scenario)
        logger.info(f"✅ Generated test video: {video_path}")
        
        # Load and analyze the generated face data
        json_path = video_path.replace('.mp4', '_faces.json')
        if os.path.exists(json_path):
            with open(json_path, 'r') as f:
                data = json.load(f)
            
            face_sequence = data['face_data']
            logger.info(f"📊 Analyzing generated face data:")
            logger.info(f"   Total frames: {len(face_sequence)}")
            
            # Check if it's a dual face scenario
            is_dual = DualVideoCompositor.is_dual_face_scenario(face_sequence)
            logger.info(f"   Dual face scenario: {is_dual}")
            
            if is_dual:
                logger.info(f"   ✅ Perfect for dual video compositing!")
            else:
                logger.info(f"   ⚠️ May not be optimal for dual video compositing")
    else:
        logger.warning("❌ Dual video compositing scenario not found")


def demonstrate_metadata_and_alignment():
    """Demonstrate metadata creation and temporal alignment"""
    logger.info("\n📊 Demonstrating Metadata and Temporal Alignment")
    logger.info("=" * 55)
    
    compositor = DualVideoCompositor()
    
    # Create test sequence
    sequence = create_consistent_dual_sequence(duration=3.0)
    left_track, right_track = compositor._separate_face_tracks(sequence)
    compositor._generate_individual_layouts(left_track, right_track)
    
    # Create metadata
    logger.info("📋 Creating dual video metadata...")
    metadata = compositor._create_dual_video_metadata(left_track, right_track, sequence)
    
    logger.info(f"   Generation Info:")
    gen_info = metadata['generation_info']
    logger.info(f"   • Compositor version: {gen_info['compositor_version']}")
    logger.info(f"   • Face positioning engine: {gen_info['face_positioning_engine']}")
    logger.info(f"   • Total frames: {gen_info['total_frames']}")
    logger.info(f"   • Dual face frames: {gen_info['dual_face_frames']}")
    
    logger.info(f"\n   Video A (Left Face):")
    video_a = metadata['video_a']
    logger.info(f"   • Face ID: {video_a['face_id']}")
    logger.info(f"   • Target region: {video_a['target_region']}")
    logger.info(f"   • Region dimensions: {video_a['region_dimensions']}")
    logger.info(f"   • Average confidence: {video_a['average_confidence']:.3f}")
    
    logger.info(f"\n   Video B (Right Face):")
    video_b = metadata['video_b']
    logger.info(f"   • Face ID: {video_b['face_id']}")
    logger.info(f"   • Target region: {video_b['target_region']}")
    logger.info(f"   • Region dimensions: {video_b['region_dimensions']}")
    logger.info(f"   • Average confidence: {video_b['average_confidence']:.3f}")
    
    # Create temporal alignment data
    logger.info(f"\n⏱️ Creating temporal alignment data...")
    alignment = compositor._create_temporal_alignment_data(left_track, right_track)
    
    frame_mapping = alignment['frame_mapping']
    logger.info(f"   Frame Mapping:")
    logger.info(f"   • Left face frames: {frame_mapping['left_face_frames']}")
    logger.info(f"   • Right face frames: {frame_mapping['right_face_frames']}")
    logger.info(f"   • Synchronized frames: {frame_mapping['synchronized_frames']}")
    
    logger.info(f"   Compositing ready: {alignment['compositing_ready']}")
    
    # Show vertical layout data
    layout_data = alignment['vertical_layout_data']
    logger.info(f"   Vertical Layout:")
    logger.info(f"   • Top region: {layout_data['top_region']}")
    logger.info(f"   • Bottom region: {layout_data['bottom_region']}")


# Helper functions for creating test data

def create_test_sequence(dual_ratio: float, total_frames: int) -> list:
    """Create a test sequence with specified dual face ratio"""
    sequence = []
    dual_frames = int(total_frames * dual_ratio)
    
    for i in range(total_frames):
        timestamp = i / 30.0
        
        if i < dual_frames:
            # Dual face frame
            faces = [
                {'x': 300, 'y': 400, 'width': 200, 'height': 200, 'confidence': 0.9, 'center_x': 400, 'center_y': 500},
                {'x': 800, 'y': 400, 'width': 180, 'height': 180, 'confidence': 0.85, 'center_x': 890, 'center_y': 490}
            ]
        else:
            # Single face frame
            faces = [
                {'x': 500, 'y': 400, 'width': 200, 'height': 200, 'confidence': 0.92, 'center_x': 600, 'center_y': 500}
            ]
        
        sequence.append({'timestamp': timestamp, 'faces': faces})
    
    return sequence


def create_consistent_dual_sequence(duration: float = 5.0) -> list:
    """Create a consistent dual face sequence"""
    sequence = []
    total_frames = int(duration * 30)
    
    for i in range(total_frames):
        timestamp = i / 30.0
        
        faces = [
            {
                'x': 300 + i, 'y': 400, 'width': 200, 'height': 200,
                'confidence': 0.92, 'center_x': 400 + i, 'center_y': 500
            },
            {
                'x': 800 + i//2, 'y': 400, 'width': 180, 'height': 180,
                'confidence': 0.88, 'center_x': 890 + i//2, 'center_y': 490
            }
        ]
        
        sequence.append({'timestamp': timestamp, 'faces': faces})
    
    return sequence


def create_moving_faces_sequence() -> list:
    """Create a sequence with moving faces for track separation demo"""
    sequence = []
    
    for i in range(60):  # 2 seconds at 30fps
        timestamp = i / 30.0
        
        # Left face moving right
        left_face = {
            'x': 200 + i * 3,
            'y': 400 + int(10 * (i % 20) / 20),  # Slight vertical movement
            'width': 200,
            'height': 200,
            'confidence': 0.93,
            'center_x': 300 + i * 3,
            'center_y': 500 + int(10 * (i % 20) / 20)
        }
        
        # Right face moving left
        right_face = {
            'x': 1200 - i * 2,
            'y': 450 + int(15 * (i % 15) / 15),  # Different vertical pattern
            'width': 190,
            'height': 190,
            'confidence': 0.89,
            'center_x': 1295 - i * 2,
            'center_y': 545 + int(15 * (i % 15) / 15)
        }
        
        sequence.append({
            'timestamp': timestamp,
            'faces': [left_face, right_face]
        })
    
    return sequence


def main():
    """Main demonstration function"""
    logger.info("🎬 Dual Video Compositing System Demonstration")
    logger.info("=" * 60)
    logger.info("Comprehensive demonstration of dual video compositing capabilities")
    
    try:
        demonstrate_dual_face_detection()
        demonstrate_enhanced_dual_positioning()
        demonstrate_face_track_separation()
        demonstrate_video_generation_with_dual_output()
        demonstrate_metadata_and_alignment()
        
        logger.info("\n" + "="*60)
        logger.info("🎉 DUAL VIDEO COMPOSITING DEMO COMPLETE")
        logger.info("="*60)
        logger.info("✅ All dual video compositing features demonstrated successfully!")
        logger.info("📁 Check dual_demo_videos/ for generated test videos")
        logger.info("🎯 System ready for dual-face video processing")
        
        logger.info("\n🎯 Key Features Demonstrated:")
        logger.info("   ✅ Automatic dual-face scenario detection")
        logger.info("   ✅ Face identification by horizontal position")
        logger.info("   ✅ Dual video generation with separate outputs")
        logger.info("   ✅ Enhanced face positioning for each track")
        logger.info("   ✅ Predictive tracking for both faces")
        logger.info("   ✅ Temporal alignment for future compositing")
        logger.info("   ✅ Comprehensive metadata and validation")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
