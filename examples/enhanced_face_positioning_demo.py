#!/usr/bin/env python3
"""
Enhanced Face Positioning System Demonstration

This script demonstrates the comprehensive face placement system for vertical video formatting
with predictive tracking, smooth transitions, and optimal composition.

Features demonstrated:
- Single face: Centered entirely within the frame
- Two faces: Vertical arrangement (leftmost top, rightmost bottom)
- Three faces: 2+1 layout (two top, one bottom center)
- Four faces: 2x2 grid layout
- Predictive tracking with 2-3 second lookahead
- Smooth transitions between layout changes
- MediaPipe integration with GPU acceleration
"""

import sys
import os
import numpy as np
import logging
from typing import List

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from reframing.video.face_positioning import (
    FacePositioningEngine, FaceLayoutType, PredictiveFaceData
)
from reframing.models.data_classes import FaceDetection

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_demo_face(x: int, y: int, width: int = 200, height: int = 200, 
                    confidence: float = 0.9) -> FaceDetection:
    """Create a demo face detection for testing"""
    return FaceDetection(
        x=x, y=y, width=width, height=height, confidence=confidence,
        center_x=x + width/2, center_y=y + height/2
    )


def demonstrate_layout_rules():
    """Demonstrate the core layout rules for different face counts"""
    logger.info("🎯 Demonstrating Enhanced Face Positioning Layout Rules")
    logger.info("=" * 60)
    
    engine = FacePositioningEngine()
    target_width, target_height = 720, 1280
    frame_width, frame_height = 1920, 1080
    
    # Test Case 1: Single Face - Centered
    logger.info("\n1️⃣ SINGLE FACE LAYOUT")
    single_face = [create_demo_face(400, 300)]
    
    layout = engine.calculate_face_positioning(
        faces=single_face,
        frame_width=frame_width,
        frame_height=frame_height,
        target_width=target_width,
        target_height=target_height,
        timestamp=0.0
    )
    
    logger.info(f"   Layout Type: {layout.layout_type.value}")
    logger.info(f"   Regions: {len(layout.regions)}")
    logger.info(f"   Face Assignment: {layout.face_assignments}")
    logger.info(f"   Crop Position: ({layout.crop_x}, {layout.crop_y})")
    logger.info(f"   Confidence: {layout.confidence:.3f}")
    
    # Test Case 2: Two Faces - Vertical Arrangement
    logger.info("\n2️⃣ DUAL VERTICAL LAYOUT")
    dual_faces = [
        create_demo_face(300, 300),  # Leftmost face
        create_demo_face(800, 300)   # Rightmost face
    ]
    
    layout = engine.calculate_face_positioning(
        faces=dual_faces,
        frame_width=frame_width,
        frame_height=frame_height,
        target_width=target_width,
        target_height=target_height,
        timestamp=1.0
    )
    
    logger.info(f"   Layout Type: {layout.layout_type.value}")
    logger.info(f"   Regions: {[r.region_id for r in layout.regions]}")
    logger.info(f"   Face Assignments: {layout.face_assignments}")
    logger.info(f"   Crop Position: ({layout.crop_x}, {layout.crop_y})")
    logger.info(f"   Confidence: {layout.confidence:.3f}")
    
    # Test Case 3: Three Faces - 2+1 Layout
    logger.info("\n3️⃣ TRIPLE 2+1 LAYOUT")
    triple_faces = [
        create_demo_face(200, 300),   # Left face
        create_demo_face(600, 300),   # Middle face
        create_demo_face(1000, 300)   # Right face
    ]
    
    layout = engine.calculate_face_positioning(
        faces=triple_faces,
        frame_width=frame_width,
        frame_height=frame_height,
        target_width=target_width,
        target_height=target_height,
        timestamp=2.0
    )
    
    logger.info(f"   Layout Type: {layout.layout_type.value}")
    logger.info(f"   Regions: {[r.region_id for r in layout.regions]}")
    logger.info(f"   Face Assignments: {layout.face_assignments}")
    logger.info(f"   Crop Position: ({layout.crop_x}, {layout.crop_y})")
    logger.info(f"   Confidence: {layout.confidence:.3f}")
    
    # Test Case 4: Four Faces - 2x2 Grid
    logger.info("\n4️⃣ QUAD GRID LAYOUT")
    quad_faces = [
        create_demo_face(200, 200),   # Top-left
        create_demo_face(800, 200),   # Top-right
        create_demo_face(200, 600),   # Bottom-left
        create_demo_face(800, 600)    # Bottom-right
    ]
    
    layout = engine.calculate_face_positioning(
        faces=quad_faces,
        frame_width=frame_width,
        frame_height=frame_height,
        target_width=target_width,
        target_height=target_height,
        timestamp=3.0
    )
    
    logger.info(f"   Layout Type: {layout.layout_type.value}")
    logger.info(f"   Regions: {[r.region_id for r in layout.regions]}")
    logger.info(f"   Face Assignments: {layout.face_assignments}")
    logger.info(f"   Crop Position: ({layout.crop_x}, {layout.crop_y})")
    logger.info(f"   Confidence: {layout.confidence:.3f}")


def demonstrate_predictive_tracking():
    """Demonstrate predictive face tracking with 2-3 second lookahead"""
    logger.info("\n🔮 Demonstrating Predictive Face Tracking")
    logger.info("=" * 50)
    
    engine = FacePositioningEngine()
    target_width, target_height = 720, 1280
    frame_width, frame_height = 1920, 1080
    
    # Simulate a face moving across the frame over time
    logger.info("\n📈 Simulating Face Movement Over Time")
    
    for i in range(10):
        timestamp = i * 0.2  # Every 200ms
        
        # Create a face that moves from left to right
        moving_face = create_demo_face(300 + i*50, 300 + i*10)
        faces = [moving_face]
        
        # Add to face history for velocity calculation
        engine.face_history.append((timestamp, faces.copy()))
        
        layout = engine.calculate_face_positioning(
            faces=faces,
            frame_width=frame_width,
            frame_height=frame_height,
            target_width=target_width,
            target_height=target_height,
            timestamp=timestamp
        )
        
        if i >= 3:  # After we have enough history for predictions
            logger.info(f"   Frame {i}: Face at ({moving_face.center_x:.0f}, {moving_face.center_y:.0f})")
            
            if layout.predictive_data:
                pred_data = layout.predictive_data[0]
                logger.info(f"            Velocity: ({pred_data.velocity[0]:.1f}, {pred_data.velocity[1]:.1f})")
                logger.info(f"            Stability: {pred_data.stability_score:.3f}")
                logger.info(f"            Predictions: {len(pred_data.predicted_positions)} future positions")
            
            if layout.segment_plan:
                logger.info(f"            Segment: {layout.segment_plan.start_time:.1f}s - {layout.segment_plan.end_time:.1f}s")
                logger.info(f"            Expected faces: {layout.segment_plan.expected_face_count}")


def demonstrate_smooth_transitions():
    """Demonstrate smooth transitions when face count changes"""
    logger.info("\n🎬 Demonstrating Smooth Layout Transitions")
    logger.info("=" * 45)
    
    engine = FacePositioningEngine()
    target_width, target_height = 720, 1280
    frame_width, frame_height = 1920, 1080
    
    # Scenario: Person enters frame (1 -> 2 faces)
    logger.info("\n📹 Scenario: Second person enters frame")
    
    # Frame 1: Single face
    single_face = [create_demo_face(400, 300)]
    layout1 = engine.calculate_face_positioning(
        faces=single_face,
        frame_width=frame_width,
        frame_height=frame_height,
        target_width=target_width,
        target_height=target_height,
        timestamp=0.0
    )
    
    logger.info(f"   Before: {layout1.layout_type.value} layout")
    logger.info(f"           Crop: ({layout1.crop_x}, {layout1.crop_y})")
    
    # Frame 2: Two faces (triggers transition)
    dual_faces = [
        create_demo_face(300, 300),
        create_demo_face(700, 300)
    ]
    layout2 = engine.calculate_face_positioning(
        faces=dual_faces,
        frame_width=frame_width,
        frame_height=frame_height,
        target_width=target_width,
        target_height=target_height,
        timestamp=0.1
    )
    
    logger.info(f"   After:  {layout2.layout_type.value} layout")
    logger.info(f"           Crop: ({layout2.crop_x}, {layout2.crop_y})")
    logger.info(f"           Transition frames: {engine.layout_transition_frames}")
    
    # Simulate transition frames
    logger.info("\n🔄 Simulating transition frames:")
    for i in range(5):
        timestamp = 0.1 + i * 0.033  # ~30fps
        layout = engine.calculate_face_positioning(
            faces=dual_faces,
            frame_width=frame_width,
            frame_height=frame_height,
            target_width=target_width,
            target_height=target_height,
            timestamp=timestamp
        )
        logger.info(f"   Frame {i+1}: Crop ({layout.crop_x}, {layout.crop_y}), "
                   f"Transition progress: {engine.layout_transition_frames}/{engine.max_transition_frames}")


def demonstrate_integration_compatibility():
    """Demonstrate integration with existing MediaPipe pipeline"""
    logger.info("\n🔗 Demonstrating MediaPipe Integration Compatibility")
    logger.info("=" * 55)
    
    logger.info("\n✅ Enhanced Face Positioning System Features:")
    logger.info("   • MediaPipe GPU acceleration with CPU fallback")
    logger.info("   • Predictive tracking with 2-3 second lookahead")
    logger.info("   • Comprehensive segment planning")
    logger.info("   • Smooth transitions between layout changes")
    logger.info("   • Professional terminology and enterprise-grade performance")
    logger.info("   • Backward compatibility with existing video processing pipeline")
    
    logger.info("\n🎯 Layout Rules Summary:")
    logger.info("   1 face  → Centered entirely within frame")
    logger.info("   2 faces → Vertical halves (leftmost top, rightmost bottom)")
    logger.info("   3 faces → 2+1 layout (two top, one bottom center)")
    logger.info("   4 faces → 2x2 grid (each face centered in quadrant)")
    
    logger.info("\n🔮 Predictive Features:")
    logger.info("   • 2.5 second lookahead analysis")
    logger.info("   • Face velocity and stability calculation")
    logger.info("   • Segment plan generation for stable layouts")
    logger.info("   • Predictive crop position smoothing")
    logger.info("   • Enhanced confidence scoring with predictive factors")


def main():
    """Main demonstration function"""
    logger.info("🚀 Enhanced Face Positioning System Demonstration")
    logger.info("=" * 60)
    logger.info("Comprehensive face placement for vertical video formatting")
    logger.info("with predictive tracking and smooth transitions")
    
    try:
        demonstrate_layout_rules()
        demonstrate_predictive_tracking()
        demonstrate_smooth_transitions()
        demonstrate_integration_compatibility()
        
        logger.info("\n✅ Demonstration completed successfully!")
        logger.info("The enhanced face positioning system is ready for production use.")
        
    except Exception as e:
        logger.error(f"❌ Demonstration failed: {str(e)}")
        raise


if __name__ == "__main__":
    main()
