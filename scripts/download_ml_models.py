#!/usr/bin/env python3
"""
Download and initialize ML models for highlights extraction
"""

import os
import sys
from pathlib import Path

def download_models():
    """Download all required ML models"""
    print("🤖 Downloading ML Models for Highlights Extraction")
    print("=" * 55)
    
    # 1. KeyBERT (uses sentence-transformers)
    print("\n📥 1. Downloading KeyBERT models...")
    try:
        from keybert import KeyBERT
        kb = KeyBERT()
        # Test extraction to trigger model download
        test_result = kb.extract_keywords("artificial intelligence technology", top_k=1)
        print(f"✅ KeyBERT ready: {test_result}")
    except Exception as e:
        print(f"❌ KeyBERT failed: {e}")
    
    # 2. Transformers sentiment analysis
    print("\n📥 2. Downloading sentiment analysis models...")
    try:
        from transformers import pipeline
        sentiment = pipeline(
            "sentiment-analysis",
            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
            return_all_scores=True
        )
        # Test to trigger model download
        test_result = sentiment("This is amazing!")
        print(f"✅ Sentiment analysis ready: {test_result[0][0]['label']}")
    except Exception as e:
        print(f"❌ Sentiment analysis failed: {e}")
    
    # 3. SentenceTransformers for embeddings
    print("\n📥 3. Downloading embedding models...")
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer("all-MiniLM-L6-v2")
        # Test to trigger model download
        test_embedding = model.encode(["test sentence"])
        print(f"✅ Embeddings ready: shape {test_embedding.shape}")
    except Exception as e:
        print(f"❌ Embeddings failed: {e}")
    
    # 4. Test scikit-learn
    print("\n📥 4. Testing scikit-learn...")
    try:
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        # Test cosine similarity
        test_sim = cosine_similarity([[1, 0]], [[0, 1]])
        print(f"✅ Scikit-learn ready: {test_sim[0][0]:.3f}")
    except Exception as e:
        print(f"❌ Scikit-learn failed: {e}")
    
    print("\n🎉 Model download completed!")
    print("\n💡 Now run: python test_with_sample_data.py")


if __name__ == '__main__':
    download_models()
