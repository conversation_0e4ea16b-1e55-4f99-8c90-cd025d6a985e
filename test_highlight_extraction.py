#!/usr/bin/env python3
"""
Test actual highlight extraction with Hindi content
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_highlight_extraction():
    """Test end-to-end highlight extraction"""
    print("TESTING HIGHLIGHT EXTRACTION WITH HINDI CONTENT")
    print("=" * 60)
    
    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector
        
        # Realistic Hindi transcript segments
        hindi_transcript = [
            {
                "text": "नमस्कार दोस्तों! आज मैं आपको एक अविश्वसनीय कहानी सुनाने जा रहा हूँ।",
                "start": 0.0,
                "end": 4.5
            },
            {
                "text": "यह रहस्य बहुत ही चौंकाने वाला है और ज्यादातर लोग नहीं जानते।",
                "start": 4.5,
                "end": 8.2
            },
            {
                "text": "अनुसंधान दिखाता है कि यह बात सच्चाई है। वाकई अद्भुत!",
                "start": 8.2,
                "end": 12.0
            },
            {
                "text": "तब जाकर मुझे एहसास हुआ कि यह जीवन बदलने वाला मोड़ था।",
                "start": 12.0,
                "end": 16.5
            },
            {
                "text": "क्या होगा अगर मैं आपको बताऊं कि यह सब कैसे हुआ? सोचिए!",
                "start": 16.5,
                "end": 21.0
            },
            {
                "text": "यह कहानी शुरू होती है एक छोटे से गांव से।",
                "start": 21.0,
                "end": 24.5
            },
            {
                "text": "वहाँ एक व्यक्ति रहता था जो बहुत ही सामान्य था।",
                "start": 24.5,
                "end": 28.0
            },
            {
                "text": "लेकिन उसके पास एक विशेष गुण था जो किसी को पता नहीं था।",
                "start": 28.0,
                "end": 32.5
            },
            {
                "text": "एक दिन अचानक कुछ ऐसा हुआ जिसने सब कुछ बदल दिया।",
                "start": 32.5,
                "end": 36.0
            },
            {
                "text": "यह घटना इतनी महत्वपूर्ण थी कि पूरा गांव हैरान रह गया।",
                "start": 36.0,
                "end": 40.5
            }
        ]
        
        # English transcript for comparison
        english_transcript = [
            {
                "text": "Hello everyone! Today I'm going to tell you an incredible story.",
                "start": 0.0,
                "end": 4.5
            },
            {
                "text": "This secret is absolutely shocking and most people don't know about it.",
                "start": 4.5,
                "end": 8.2
            },
            {
                "text": "Research shows that this is the truth. Really amazing!",
                "start": 8.2,
                "end": 12.0
            },
            {
                "text": "That's when I realized this was a life changing turning point.",
                "start": 12.0,
                "end": 16.5
            },
            {
                "text": "What if I told you how this all happened? Just imagine!",
                "start": 16.5,
                "end": 21.0
            },
            {
                "text": "This story begins in a small village.",
                "start": 21.0,
                "end": 24.5
            },
            {
                "text": "There lived a person who was very ordinary.",
                "start": 24.5,
                "end": 28.0
            },
            {
                "text": "But he had a special quality that nobody knew about.",
                "start": 28.0,
                "end": 32.5
            },
            {
                "text": "One day suddenly something happened that changed everything.",
                "start": 32.5,
                "end": 36.0
            },
            {
                "text": "This event was so important that the whole village was amazed.",
                "start": 36.0,
                "end": 40.5
            }
        ]
        
        print("\n1. TESTING HINDI HIGHLIGHT EXTRACTION")
        print("-" * 50)
        
        start_time = time.time()
        
        # Test Hindi highlights
        hindi_detector = VideoHighlightsDetector(language="auto")
        hindi_highlights = hindi_detector.find_best_highlights(
            hindi_transcript,
            max_highlights=3,
            video_duration=40.5
        )
        
        hindi_time = time.time() - start_time
        
        print(f"Processing time: {hindi_time:.2f} seconds")
        print(f"Detected language: {hindi_detector.detected_language}")
        print(f"Language confidence: {hindi_detector.language_confidence:.2f}")
        print(f"Highlights found: {len(hindi_highlights)}")
        
        for i, highlight in enumerate(hindi_highlights):
            print(f"\nHighlight {i+1}:")
            print(f"  Time: {highlight['start_time']:.1f}s - {highlight['end_time']:.1f}s ({highlight['duration']:.1f}s)")
            print(f"  Score: {highlight['engagement_score']:.3f}")
            print(f"  Text: {highlight['text'][:80]}...")
            if 'metadata' in highlight:
                print(f"  Algorithm: {highlight.get('algorithm', 'N/A')}")
        
        print("\n2. TESTING ENGLISH HIGHLIGHT EXTRACTION")
        print("-" * 50)
        
        start_time = time.time()
        
        # Test English highlights
        english_detector = VideoHighlightsDetector(language="auto")
        english_highlights = english_detector.find_best_highlights(
            english_transcript,
            max_highlights=3,
            video_duration=40.5
        )
        
        english_time = time.time() - start_time
        
        print(f"Processing time: {english_time:.2f} seconds")
        print(f"Detected language: {english_detector.detected_language}")
        print(f"Language confidence: {english_detector.language_confidence:.2f}")
        print(f"Highlights found: {len(english_highlights)}")
        
        for i, highlight in enumerate(english_highlights):
            print(f"\nHighlight {i+1}:")
            print(f"  Time: {highlight['start_time']:.1f}s - {highlight['end_time']:.1f}s ({highlight['duration']:.1f}s)")
            print(f"  Score: {highlight['engagement_score']:.3f}")
            print(f"  Text: {highlight['text'][:80]}...")
            if 'metadata' in highlight:
                print(f"  Algorithm: {highlight.get('algorithm', 'N/A')}")
        
        print("\n3. PERFORMANCE COMPARISON")
        print("-" * 50)
        
        print(f"Hindi processing time: {hindi_time:.2f}s")
        print(f"English processing time: {english_time:.2f}s")
        print(f"Time ratio (Hindi/English): {hindi_time/english_time:.2f}")
        
        # Compare average scores
        hindi_avg_score = sum(h['engagement_score'] for h in hindi_highlights) / len(hindi_highlights) if hindi_highlights else 0
        english_avg_score = sum(h['engagement_score'] for h in english_highlights) / len(english_highlights) if english_highlights else 0
        
        print(f"Hindi average engagement score: {hindi_avg_score:.3f}")
        print(f"English average engagement score: {english_avg_score:.3f}")
        
        print("\n4. QUALITY ASSESSMENT")
        print("-" * 50)
        
        # Check if highlights are reasonable
        quality_checks = []
        
        # Check if we got highlights
        quality_checks.append(len(hindi_highlights) > 0)
        quality_checks.append(len(english_highlights) > 0)
        
        # Check if highlights have reasonable durations
        for highlight in hindi_highlights + english_highlights:
            quality_checks.append(10.0 <= highlight['duration'] <= 30.0)
        
        # Check if highlights have reasonable scores
        for highlight in hindi_highlights + english_highlights:
            quality_checks.append(0.0 <= highlight['engagement_score'] <= 1.0)
        
        # Check if Hindi highlights contain engaging content
        hindi_engaging_content = any(
            any(pattern in highlight['text'].lower() for pattern in 
                ['अविश्वसनीय', 'चौंकाने वाला', 'रहस्य', 'क्या होगा अगर'])
            for highlight in hindi_highlights
        )
        quality_checks.append(hindi_engaging_content)
        
        # Check if English highlights contain engaging content
        english_engaging_content = any(
            any(pattern in highlight['text'].lower() for pattern in 
                ['incredible', 'shocking', 'secret', 'what if'])
            for highlight in english_highlights
        )
        quality_checks.append(english_engaging_content)
        
        quality_score = sum(quality_checks) / len(quality_checks) * 100
        
        print(f"Quality score: {quality_score:.1f}%")
        print(f"Hindi highlights contain engaging patterns: {hindi_engaging_content}")
        print(f"English highlights contain engaging patterns: {english_engaging_content}")
        
        print("\n" + "=" * 60)
        
        if quality_score >= 80:
            print("🎉 HIGHLIGHT EXTRACTION TEST PASSED!")
            print("✓ Both Hindi and English highlights are generated correctly")
            print("✓ Language detection works accurately")
            print("✓ Engagement scoring identifies relevant content")
            print("✓ Performance is acceptable for both languages")
        else:
            print("⚠️ HIGHLIGHT EXTRACTION NEEDS IMPROVEMENT")
            print(f"Quality score: {quality_score:.1f}% (target: 80%+)")
        
        return quality_score >= 80
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_highlight_extraction()
    
    if success:
        print("\n✅ HINDI HIGHLIGHT EXTRACTION IS WORKING CORRECTLY!")
    else:
        print("\n❌ HIGHLIGHT EXTRACTION TEST FAILED")
    
    return success

if __name__ == "__main__":
    main()
