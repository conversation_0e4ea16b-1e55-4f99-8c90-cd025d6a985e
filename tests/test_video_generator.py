#!/usr/bin/env python3
"""
Test suite for the video test generator

This test validates the video generation system for the enhanced face positioning system.
"""

import unittest
import tempfile
import shutil
import os
import json
import cv2
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tools.video_test_generator import VideoTestGenerator, TestFace, VideoScenario


class TestVideoGenerator(unittest.TestCase):
    """Test suite for video test generator"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.generator = VideoTestGenerator(output_dir=self.temp_dir)

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_test_face_creation(self):
        """Test TestFace data structure"""
        face = TestFace(
            id=1,
            start_time=0.0,
            end_time=5.0,
            start_pos=(100, 200),
            end_pos=(300, 400),
            size=(150, 150),
            confidence=0.9,
            movement_type='linear',
            color=(255, 0, 0)
        )
        
        self.assertEqual(face.id, 1)
        self.assertEqual(face.start_time, 0.0)
        self.assertEqual(face.end_time, 5.0)
        self.assertEqual(face.movement_type, 'linear')

    def test_video_scenario_creation(self):
        """Test VideoScenario data structure"""
        test_face = TestFace(1, 0.0, 5.0, (100, 100), (200, 200), (150, 150), 0.9, 'linear', (255, 0, 0))
        
        scenario = VideoScenario(
            name="test_scenario",
            duration=5.0,
            description="Test scenario",
            faces=[test_face],
            target_features=["test_feature"]
        )
        
        self.assertEqual(scenario.name, "test_scenario")
        self.assertEqual(scenario.duration, 5.0)
        self.assertEqual(len(scenario.faces), 1)
        self.assertEqual(scenario.target_features, ["test_feature"])

    def test_generator_initialization(self):
        """Test video generator initialization"""
        self.assertEqual(self.generator.width, 1920)
        self.assertEqual(self.generator.height, 1080)
        self.assertEqual(self.generator.fps, 30)
        self.assertEqual(self.generator.target_width, 720)
        self.assertEqual(self.generator.target_height, 1280)
        self.assertTrue(Path(self.temp_dir).exists())

    def test_create_test_scenarios(self):
        """Test test scenario creation"""
        scenarios = self.generator._create_test_scenarios()
        
        self.assertGreater(len(scenarios), 0)
        
        # Check that all required scenarios exist
        scenario_names = [s.name for s in scenarios]
        expected_scenarios = [
            "layout_transitions",
            "predictive_tracking", 
            "stability_test",
            "realworld_simulation"
        ]
        
        for expected in expected_scenarios:
            self.assertIn(expected, scenario_names)
        
        # Validate scenario structure
        for scenario in scenarios:
            self.assertIsInstance(scenario.name, str)
            self.assertGreater(scenario.duration, 0)
            self.assertIsInstance(scenario.description, str)
            self.assertIsInstance(scenario.faces, list)
            self.assertIsInstance(scenario.target_features, list)

    def test_face_position_calculation(self):
        """Test face position calculation for different movement types"""
        # Test stationary movement
        face = TestFace(1, 0.0, 5.0, (100, 200), (300, 400), (150, 150), 0.9, 'stationary', (255, 0, 0))
        pos = self.generator._calculate_face_position(face, 2.5)
        self.assertEqual(pos, (100, 200))
        
        # Test linear movement
        face = TestFace(1, 0.0, 4.0, (100, 200), (300, 400), (150, 150), 0.9, 'linear', (255, 0, 0))
        pos = self.generator._calculate_face_position(face, 2.0)  # Halfway through
        self.assertEqual(pos, (200, 300))
        
        # Test circular movement
        face = TestFace(1, 0.0, 4.0, (100, 200), (300, 400), (150, 150), 0.9, 'circular', (255, 0, 0))
        pos = self.generator._calculate_face_position(face, 1.0)
        self.assertIsInstance(pos, tuple)
        self.assertEqual(len(pos), 2)

    def test_active_faces_filtering(self):
        """Test filtering of active faces by timestamp"""
        faces = [
            TestFace(1, 0.0, 5.0, (100, 100), (200, 200), (150, 150), 0.9, 'linear', (255, 0, 0)),
            TestFace(2, 2.0, 7.0, (300, 300), (400, 400), (150, 150), 0.9, 'linear', (0, 255, 0)),
            TestFace(3, 6.0, 10.0, (500, 500), (600, 600), (150, 150), 0.9, 'linear', (0, 0, 255))
        ]
        
        # At timestamp 1.0, only face 1 should be active
        active = self.generator._get_active_faces(faces, 1.0)
        self.assertEqual(len(active), 1)
        self.assertEqual(active[0].id, 1)
        
        # At timestamp 3.0, faces 1 and 2 should be active
        active = self.generator._get_active_faces(faces, 3.0)
        self.assertEqual(len(active), 2)
        self.assertEqual(set(f.id for f in active), {1, 2})
        
        # At timestamp 7.0, faces 2 and 3 should be active (face 2: 2.0-7.0, face 3: 6.0-10.0)
        active = self.generator._get_active_faces(faces, 7.0)
        self.assertEqual(len(active), 2)
        self.assertEqual(set(f.id for f in active), {2, 3})

    def test_face_detection_creation(self):
        """Test creation of FaceDetection objects"""
        face = TestFace(1, 0.0, 5.0, (400, 300), (400, 300), (200, 200), 0.9, 'stationary', (255, 0, 0))
        detection = self.generator._create_face_detection(face, (400, 300), 1.0)
        
        self.assertIsNotNone(detection)
        self.assertEqual(detection.width, 200)
        self.assertEqual(detection.height, 200)
        self.assertAlmostEqual(detection.confidence, 0.9, delta=0.1)  # Allow for noise
        self.assertAlmostEqual(detection.center_x, 400, delta=10)  # Allow for positioning adjustment
        self.assertAlmostEqual(detection.center_y, 300, delta=10)

    def test_frame_background_creation(self):
        """Test frame background creation"""
        frame = self.generator._create_frame_background()
        
        self.assertEqual(frame.shape, (self.generator.height, self.generator.width, 3))
        self.assertEqual(frame.dtype, 'uint8')
        
        # Check that it's not all zeros (should have grid pattern)
        self.assertGreater(frame.sum(), 0)

    def test_data_serialization(self):
        """Test JSON serialization of face detection and layout data"""
        from reframing.models.data_classes import FaceDetection
        from reframing.video.face_positioning import FaceLayoutType, FaceRegion, FacePositionLayout
        
        # Test face detection serialization
        detection = FaceDetection(x=100, y=200, width=150, height=150, confidence=0.9, center_x=175, center_y=275)
        face_dict = self.generator._face_detection_to_dict(detection)
        
        expected_keys = ['x', 'y', 'width', 'height', 'confidence', 'center_x', 'center_y']
        for key in expected_keys:
            self.assertIn(key, face_dict)
        
        # Test layout serialization
        region = FaceRegion(region_id="center", x=0, y=0, width=720, height=1280, center_x=360, center_y=640)
        layout = FacePositionLayout(
            layout_type=FaceLayoutType.SINGLE_CENTERED,
            regions=[region],
            face_assignments={0: "center"},
            crop_x=100,
            crop_y=200,
            confidence=0.95
        )
        
        layout_dict = self.generator._layout_to_dict(layout)
        
        expected_keys = ['layout_type', 'crop_x', 'crop_y', 'confidence', 'regions', 'face_assignments']
        for key in expected_keys:
            self.assertIn(key, layout_dict)

    def test_short_video_generation(self):
        """Test generation of a short test video"""
        # Create a simple test scenario
        test_face = TestFace(1, 0.0, 1.0, (400, 300), (500, 400), (150, 150), 0.9, 'linear', (255, 0, 0))
        scenario = VideoScenario(
            name="short_test",
            duration=1.0,
            description="Short test video",
            faces=[test_face],
            target_features=["basic_test"]
        )
        
        # Generate video
        video_path = self.generator._generate_scenario_video(scenario)
        
        # Verify video file exists
        self.assertTrue(os.path.exists(video_path))
        
        # Verify JSON file exists
        json_path = video_path.replace('.mp4', '_faces.json')
        self.assertTrue(os.path.exists(json_path))
        
        # Verify video properties
        cap = cv2.VideoCapture(video_path)
        self.assertTrue(cap.isOpened())
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.release()
        
        self.assertEqual(width, 1920)
        self.assertEqual(height, 1080)
        self.assertAlmostEqual(fps, 30, delta=1)
        self.assertAlmostEqual(frame_count, 30, delta=2)  # 1 second at 30fps
        
        # Verify JSON data
        with open(json_path, 'r') as f:
            data = json.load(f)
        
        self.assertIn('scenario', data)
        self.assertIn('video_info', data)
        self.assertIn('face_data', data)
        
        self.assertEqual(data['scenario']['name'], 'short_test')
        self.assertEqual(data['video_info']['width'], 1920)
        self.assertEqual(data['video_info']['height'], 1080)
        self.assertGreater(len(data['face_data']), 0)

    def test_performance_report_generation(self):
        """Test performance report generation"""
        # Generate a short video first
        test_face = TestFace(1, 0.0, 1.0, (400, 300), (500, 400), (150, 150), 0.9, 'linear', (255, 0, 0))
        scenario = VideoScenario(
            name="perf_test",
            duration=1.0,
            description="Performance test video",
            faces=[test_face],
            target_features=["performance_test"]
        )
        
        video_path = self.generator._generate_scenario_video(scenario)
        generated_videos = {"perf_test": video_path}
        
        # Generate performance report
        report_path = self.generator.generate_performance_report(generated_videos)
        
        # Verify report exists
        self.assertTrue(os.path.exists(report_path))
        
        # Verify report content
        with open(report_path, 'r') as f:
            report_data = json.load(f)
        
        self.assertIn('generation_timestamp', report_data)
        self.assertIn('scenarios', report_data)
        self.assertIn('summary', report_data)
        
        self.assertIn('perf_test', report_data['scenarios'])
        scenario_data = report_data['scenarios']['perf_test']
        
        expected_keys = ['duration', 'total_frames', 'target_features', 'layout_transitions', 
                        'average_confidence', 'face_count_distribution']
        for key in expected_keys:
            self.assertIn(key, scenario_data)

    def test_color_definitions(self):
        """Test that color definitions are valid"""
        colors = self.generator.colors
        
        required_colors = ['face_box', 'region_box', 'crop_box', 'prediction', 'text', 'background']
        for color_name in required_colors:
            self.assertIn(color_name, colors)
            color = colors[color_name]
            self.assertIsInstance(color, tuple)
            self.assertEqual(len(color), 3)
            for component in color:
                self.assertIsInstance(component, int)
                self.assertGreaterEqual(component, 0)
                self.assertLessEqual(component, 255)


if __name__ == '__main__':
    unittest.main()
