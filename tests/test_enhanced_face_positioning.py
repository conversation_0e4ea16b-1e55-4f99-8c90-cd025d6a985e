#!/usr/bin/env python3
"""
Test suite for the enhanced face positioning system with predictive tracking

This test demonstrates the comprehensive face placement system for vertical video formatting
with 2-3 second lookahead analysis and smooth transitions.
"""

import unittest
import numpy as np
from unittest.mock import Mock, patch
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from reframing.video.face_positioning import (
    FacePositioningEngine, FaceLayoutType, FaceRegion, FacePositionLayout,
    PredictiveFaceData, SegmentPlan
)
from reframing.models.data_classes import FaceDetection


class TestEnhancedFacePositioning(unittest.TestCase):
    """Test suite for enhanced face positioning with predictive tracking"""

    def setUp(self):
        """Set up test fixtures"""
        self.engine = FacePositioningEngine()
        self.target_width = 720
        self.target_height = 1280
        self.frame_width = 1920
        self.frame_height = 1080

    def create_test_face(self, x: int, y: int, width: int = 200, height: int = 200, 
                        confidence: float = 0.9) -> FaceDetection:
        """Create a test face detection"""
        return FaceDetection(
            x=x, y=y, width=width, height=height, confidence=confidence,
            center_x=x + width/2, center_y=y + height/2
        )

    def test_single_face_centered_layout(self):
        """Test single face positioning - should be centered entirely"""
        faces = [self.create_test_face(400, 300)]
        
        layout = self.engine.calculate_face_positioning(
            faces=faces,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.0
        )
        
        self.assertEqual(layout.layout_type, FaceLayoutType.SINGLE_CENTERED)
        self.assertEqual(len(layout.regions), 1)
        self.assertEqual(layout.regions[0].region_id, "center")
        self.assertGreater(layout.confidence, 0.5)
        
        # Check that the region covers the full target area
        region = layout.regions[0]
        self.assertEqual(region.width, self.target_width)
        self.assertEqual(region.height, self.target_height)
        self.assertEqual(region.center_x, self.target_width / 2)
        self.assertEqual(region.center_y, self.target_height / 2)

    def test_dual_vertical_layout(self):
        """Test two faces positioning - leftmost top, rightmost bottom"""
        faces = [
            self.create_test_face(300, 300),  # Leftmost face
            self.create_test_face(800, 300)   # Rightmost face
        ]
        
        layout = self.engine.calculate_face_positioning(
            faces=faces,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.0
        )
        
        self.assertEqual(layout.layout_type, FaceLayoutType.DUAL_VERTICAL)
        self.assertEqual(len(layout.regions), 2)
        
        # Check region assignments
        self.assertIn("top_half", [r.region_id for r in layout.regions])
        self.assertIn("bottom_half", [r.region_id for r in layout.regions])
        
        # Verify face assignments (leftmost to top, rightmost to bottom)
        self.assertEqual(layout.face_assignments[0], "top_half")
        self.assertEqual(layout.face_assignments[1], "bottom_half")

    def test_triple_2_plus_1_layout(self):
        """Test three faces positioning - 2+1 layout"""
        faces = [
            self.create_test_face(200, 300),  # Left face
            self.create_test_face(600, 300),  # Middle face  
            self.create_test_face(1000, 300) # Right face
        ]
        
        layout = self.engine.calculate_face_positioning(
            faces=faces,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.0
        )
        
        self.assertEqual(layout.layout_type, FaceLayoutType.TRIPLE_2_PLUS_1)
        self.assertEqual(len(layout.regions), 3)
        
        # Check region IDs
        region_ids = [r.region_id for r in layout.regions]
        self.assertIn("top_left", region_ids)
        self.assertIn("top_right", region_ids)
        self.assertIn("bottom_center", region_ids)
        
        # Verify assignments
        self.assertEqual(layout.face_assignments[0], "top_left")
        self.assertEqual(layout.face_assignments[1], "top_right")
        self.assertEqual(layout.face_assignments[2], "bottom_center")

    def test_quad_grid_layout(self):
        """Test four faces positioning - 2x2 grid"""
        faces = [
            self.create_test_face(200, 200),   # Top-left
            self.create_test_face(800, 200),   # Top-right
            self.create_test_face(200, 600),   # Bottom-left
            self.create_test_face(800, 600)    # Bottom-right
        ]
        
        layout = self.engine.calculate_face_positioning(
            faces=faces,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.0
        )
        
        self.assertEqual(layout.layout_type, FaceLayoutType.QUAD_GRID)
        self.assertEqual(len(layout.regions), 4)
        
        # Check all quadrant regions exist
        region_ids = [r.region_id for r in layout.regions]
        expected_regions = ["top_left", "top_right", "bottom_left", "bottom_right"]
        for expected in expected_regions:
            self.assertIn(expected, region_ids)

    def test_predictive_face_data_analysis(self):
        """Test predictive face data analysis for 2-3 second lookahead"""
        faces = [self.create_test_face(400, 300)]
        
        # Add some face history to enable velocity calculation
        for i in range(5):
            timestamp = i * 0.2  # Every 200ms
            hist_faces = [self.create_test_face(400 + i*10, 300)]  # Moving right
            self.engine.face_history.append((timestamp, hist_faces))
        
        predictive_data = self.engine._analyze_predictive_face_data(faces, 1.0)
        
        self.assertEqual(len(predictive_data), 1)
        pred_data = predictive_data[0]
        
        # Check that predictions were generated
        self.assertEqual(len(pred_data.predicted_positions), 25)  # 2.5 seconds at 100ms intervals
        self.assertGreater(pred_data.stability_score, 0.0)
        
        # Check velocity calculation (should be moving right)
        self.assertGreater(pred_data.velocity[0], 0)  # Positive X velocity

    def test_segment_plan_generation(self):
        """Test comprehensive segment plan generation"""
        faces = [self.create_test_face(400, 300)]
        
        # Create predictive data
        predictive_data = [PredictiveFaceData(
            face=faces[0],
            predicted_positions=[(400 + i*5, 300) for i in range(25)],
            stability_score=0.8,
            velocity=(25.0, 0.0)
        )]
        
        current_segment = self.engine._update_segment_plans(faces, 0.0, predictive_data, len(faces))
        
        self.assertIsNotNone(current_segment)
        self.assertEqual(current_segment.expected_face_count, 1)
        self.assertEqual(current_segment.layout_type, FaceLayoutType.SINGLE_CENTERED)
        self.assertGreater(current_segment.stability_score, 0.0)

    def test_smooth_layout_transitions(self):
        """Test smooth transitions when face count changes"""
        # Start with single face
        single_face = [self.create_test_face(400, 300)]
        layout1 = self.engine.calculate_face_positioning(
            faces=single_face,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.0
        )
        
        # Add second face (should trigger transition)
        dual_faces = [
            self.create_test_face(300, 300),
            self.create_test_face(700, 300)
        ]
        layout2 = self.engine.calculate_face_positioning(
            faces=dual_faces,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.1
        )
        
        # Verify layout type changed
        self.assertEqual(layout1.layout_type, FaceLayoutType.SINGLE_CENTERED)
        self.assertEqual(layout2.layout_type, FaceLayoutType.DUAL_VERTICAL)
        
        # Check that transition smoothing was applied
        self.assertGreater(self.engine.layout_transition_frames, 0)

    def test_face_consistency_across_frames(self):
        """Test that face assignments remain consistent for stable faces"""
        faces = [
            self.create_test_face(300, 300, confidence=0.95),  # Stable face
            self.create_test_face(700, 300, confidence=0.85)   # Less stable face
        ]
        
        # Process multiple frames
        layouts = []
        for i in range(5):
            timestamp = i * 0.1
            layout = self.engine.calculate_face_positioning(
                faces=faces,
                frame_width=self.frame_width,
                frame_height=self.frame_height,
                target_width=self.target_width,
                target_height=self.target_height,
                timestamp=timestamp
            )
            layouts.append(layout)
        
        # Check that assignments remain consistent
        for i in range(1, len(layouts)):
            self.assertEqual(layouts[i].layout_type, layouts[0].layout_type)
            # Face assignments should be stable for consistent faces
            self.assertEqual(layouts[i].face_assignments, layouts[0].face_assignments)

    def test_gpu_acceleration_compatibility(self):
        """Test that the system works with MediaPipe GPU acceleration"""
        # This test ensures the enhanced system maintains compatibility
        # with the existing MediaPipe GPU acceleration
        faces = [self.create_test_face(400, 300)]
        
        layout = self.engine.calculate_face_positioning(
            faces=faces,
            frame_width=self.frame_width,
            frame_height=self.frame_height,
            target_width=self.target_width,
            target_height=self.target_height,
            timestamp=0.0
        )
        
        # Should work without errors and produce valid layout
        self.assertIsNotNone(layout)
        self.assertIsInstance(layout, FacePositionLayout)
        self.assertGreater(layout.confidence, 0.0)


if __name__ == '__main__':
    unittest.main()
