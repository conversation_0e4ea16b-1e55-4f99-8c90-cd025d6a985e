#!/usr/bin/env python3
"""
Test suite for the dual video compositor system

This test validates the dual video compositing functionality for two-face scenarios.
"""

import unittest
import tempfile
import shutil
import os
import json
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from reframing.video.dual_video_compositor import (
    DualVideoCompositor, DualVideoOutput, FaceTrack
)
from reframing.video.enhanced_dual_positioning import EnhancedDualFacePositioning
from reframing.models.data_classes import FaceDetection


class TestDualVideoCompositor(unittest.TestCase):
    """Test suite for dual video compositor"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.compositor = DualVideoCompositor()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_test_face_detection(self, x: int, y: int, width: int = 200, height: int = 200,
                                  confidence: float = 0.9) -> FaceDetection:
        """Create a test face detection"""
        return FaceDetection(
            x=x, y=y, width=width, height=height, confidence=confidence,
            center_x=x + width/2, center_y=y + height/2
        )

    def create_dual_face_data_sequence(self, duration: float = 5.0, fps: int = 30) -> list:
        """Create a test dual face data sequence"""
        sequence = []
        total_frames = int(duration * fps)
        
        for frame in range(total_frames):
            timestamp = frame / fps
            
            # Create two faces moving slightly
            left_face = {
                'x': 300 + frame * 2,
                'y': 400,
                'width': 200,
                'height': 200,
                'confidence': 0.92,
                'center_x': 400 + frame * 2,
                'center_y': 500
            }
            
            right_face = {
                'x': 800 + frame * 1,
                'y': 400,
                'width': 180,
                'height': 180,
                'confidence': 0.88,
                'center_x': 890 + frame * 1,
                'center_y': 490
            }
            
            sequence.append({
                'timestamp': timestamp,
                'frame_index': frame,
                'faces': [left_face, right_face]
            })
        
        return sequence

    def test_compositor_initialization(self):
        """Test dual video compositor initialization"""
        self.assertEqual(self.compositor.target_width, 720)
        self.assertEqual(self.compositor.target_height, 1280)
        self.assertEqual(self.compositor.region_height, 640)
        self.assertEqual(self.compositor.fps, 30)

    def test_dual_face_scenario_detection(self):
        """Test detection of dual face scenarios"""
        # Test positive case
        dual_sequence = self.create_dual_face_data_sequence()
        self.assertTrue(DualVideoCompositor.is_dual_face_scenario(dual_sequence))
        
        # Test negative case - single face
        single_sequence = []
        for i in range(100):
            single_sequence.append({
                'timestamp': i * 0.033,
                'faces': [{'x': 400, 'y': 300, 'width': 200, 'height': 200, 'confidence': 0.9}]
            })
        self.assertFalse(DualVideoCompositor.is_dual_face_scenario(single_sequence))
        
        # Test edge case - mixed scenario
        mixed_sequence = []
        for i in range(100):
            if i < 30:  # 30% dual faces
                faces = [
                    {'x': 300, 'y': 300, 'width': 200, 'height': 200, 'confidence': 0.9},
                    {'x': 800, 'y': 300, 'width': 200, 'height': 200, 'confidence': 0.9}
                ]
            else:  # 70% single face
                faces = [{'x': 400, 'y': 300, 'width': 200, 'height': 200, 'confidence': 0.9}]
            
            mixed_sequence.append({
                'timestamp': i * 0.033,
                'faces': faces
            })
        self.assertFalse(DualVideoCompositor.is_dual_face_scenario(mixed_sequence))

    def test_face_data_validation(self):
        """Test validation of dual face data"""
        # Valid dual face data
        valid_sequence = self.create_dual_face_data_sequence()
        self.assertTrue(self.compositor._validate_dual_face_data(valid_sequence))
        
        # Invalid - empty sequence
        self.assertFalse(self.compositor._validate_dual_face_data([]))
        
        # Invalid - insufficient dual faces
        invalid_sequence = []
        for i in range(100):
            faces = [{'x': 400, 'y': 300, 'width': 200, 'height': 200, 'confidence': 0.9}]
            invalid_sequence.append({'timestamp': i * 0.033, 'faces': faces})
        self.assertFalse(self.compositor._validate_dual_face_data(invalid_sequence))

    def test_face_track_separation(self):
        """Test separation of faces into left and right tracks"""
        sequence = self.create_dual_face_data_sequence(duration=2.0)
        left_track, right_track = self.compositor._separate_face_tracks(sequence)
        
        # Check track properties
        self.assertEqual(left_track.face_id, "left")
        self.assertEqual(right_track.face_id, "right")
        self.assertEqual(left_track.target_region, "top_half")
        self.assertEqual(right_track.target_region, "bottom_half")
        
        # Check face counts
        self.assertEqual(len(left_track.face_detections), len(sequence))
        self.assertEqual(len(right_track.face_detections), len(sequence))
        self.assertEqual(len(left_track.timestamps), len(sequence))
        self.assertEqual(len(right_track.timestamps), len(sequence))
        
        # Check face ordering (left face should have smaller x-coordinate)
        for i in range(len(left_track.face_detections)):
            left_face = left_track.face_detections[i]
            right_face = right_track.face_detections[i]
            self.assertLess(left_face.center_x, right_face.center_x)

    def test_individual_layout_generation(self):
        """Test generation of individual layouts for each face track"""
        sequence = self.create_dual_face_data_sequence(duration=1.0)
        left_track, right_track = self.compositor._separate_face_tracks(sequence)
        
        # Generate layouts
        self.compositor._generate_individual_layouts(left_track, right_track)
        
        # Check that layouts were generated
        self.assertEqual(len(left_track.layouts), len(left_track.face_detections))
        self.assertEqual(len(right_track.layouts), len(right_track.face_detections))
        
        # Check layout properties
        for layout in left_track.layouts:
            self.assertIsNotNone(layout)
            self.assertGreater(layout.confidence, 0.0)
        
        for layout in right_track.layouts:
            self.assertIsNotNone(layout)
            self.assertGreater(layout.confidence, 0.0)

    def test_crop_filter_creation(self):
        """Test creation of crop filters for face tracks"""
        sequence = self.create_dual_face_data_sequence(duration=1.0)
        left_track, right_track = self.compositor._separate_face_tracks(sequence)
        self.compositor._generate_individual_layouts(left_track, right_track)
        
        # Test crop filter creation
        left_filter = self.compositor._create_face_track_crop_filter(left_track)
        right_filter = self.compositor._create_face_track_crop_filter(right_track)
        
        # Check filter format
        self.assertIn("crop=", left_filter)
        self.assertIn("crop=", right_filter)
        self.assertIn(str(self.compositor.target_width), left_filter)
        self.assertIn(str(self.compositor.region_height), left_filter)

    def test_metadata_creation(self):
        """Test creation of dual video metadata"""
        sequence = self.create_dual_face_data_sequence(duration=2.0)
        left_track, right_track = self.compositor._separate_face_tracks(sequence)
        self.compositor._generate_individual_layouts(left_track, right_track)
        
        metadata = self.compositor._create_dual_video_metadata(left_track, right_track, sequence)
        
        # Check metadata structure
        self.assertIn('generation_info', metadata)
        self.assertIn('video_a', metadata)
        self.assertIn('video_b', metadata)
        self.assertIn('synchronization', metadata)
        
        # Check video A metadata
        video_a = metadata['video_a']
        self.assertEqual(video_a['face_id'], 'left')
        self.assertEqual(video_a['target_region'], 'top_half')
        self.assertGreater(video_a['average_confidence'], 0.0)
        
        # Check video B metadata
        video_b = metadata['video_b']
        self.assertEqual(video_b['face_id'], 'right')
        self.assertEqual(video_b['target_region'], 'bottom_half')
        self.assertGreater(video_b['average_confidence'], 0.0)

    def test_temporal_alignment_data(self):
        """Test creation of temporal alignment data"""
        sequence = self.create_dual_face_data_sequence(duration=1.0)
        left_track, right_track = self.compositor._separate_face_tracks(sequence)
        
        alignment_data = self.compositor._create_temporal_alignment_data(left_track, right_track)
        
        # Check alignment structure
        self.assertIn('frame_mapping', alignment_data)
        self.assertIn('timestamp_alignment', alignment_data)
        self.assertIn('compositing_ready', alignment_data)
        self.assertIn('vertical_layout_data', alignment_data)
        
        # Check frame mapping
        frame_mapping = alignment_data['frame_mapping']
        self.assertEqual(frame_mapping['left_face_frames'], len(left_track.timestamps))
        self.assertEqual(frame_mapping['right_face_frames'], len(right_track.timestamps))
        self.assertEqual(frame_mapping['synchronized_frames'], len(sequence))
        
        # Check compositing readiness
        self.assertTrue(alignment_data['compositing_ready'])

    def test_confidence_calculation(self):
        """Test average confidence calculation for face tracks"""
        # Create face detections with known confidences
        faces = [
            self.create_test_face_detection(300, 300, confidence=0.9),
            self.create_test_face_detection(400, 300, confidence=0.8),
            self.create_test_face_detection(500, 300, confidence=0.95)
        ]
        
        track = FaceTrack(
            face_id="test",
            face_detections=faces,
            timestamps=[0.0, 0.1, 0.2],
            layouts=[],
            target_region="top_half"
        )
        
        avg_confidence = self.compositor._calculate_average_confidence(track)
        expected_confidence = (0.9 + 0.8 + 0.95) / 3
        self.assertAlmostEqual(avg_confidence, expected_confidence, places=3)

    def test_debug_info(self):
        """Test debug information retrieval"""
        debug_info = self.compositor.get_debug_info()
        
        # Check debug info structure
        self.assertIn('compositor_config', debug_info)
        self.assertIn('face_engines', debug_info)
        
        # Check compositor config
        config = debug_info['compositor_config']
        self.assertEqual(config['target_width'], 720)
        self.assertEqual(config['target_height'], 1280)
        self.assertEqual(config['region_height'], 640)
        self.assertEqual(config['fps'], 30)


class TestEnhancedDualFacePositioning(unittest.TestCase):
    """Test suite for enhanced dual face positioning integration"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.enhanced_positioning = EnhancedDualFacePositioning()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_dual_face_sequence(self):
        """Create test dual face sequence"""
        sequence = []
        for i in range(60):  # 2 seconds at 30fps
            timestamp = i / 30.0
            faces = [
                {
                    'x': 300, 'y': 400, 'width': 200, 'height': 200,
                    'confidence': 0.92, 'center_x': 400, 'center_y': 500
                },
                {
                    'x': 800, 'y': 400, 'width': 180, 'height': 180,
                    'confidence': 0.88, 'center_x': 890, 'center_y': 490
                }
            ]
            sequence.append({'timestamp': timestamp, 'faces': faces})
        return sequence

    def test_enhanced_positioning_initialization(self):
        """Test enhanced dual face positioning initialization"""
        self.assertIsNotNone(self.enhanced_positioning.face_positioning_engine)
        self.assertIsNotNone(self.enhanced_positioning.dual_video_compositor)
        self.assertEqual(self.enhanced_positioning.dual_face_threshold, 0.7)
        self.assertTrue(self.enhanced_positioning.enable_dual_output)

    def test_face_data_analysis(self):
        """Test analysis of face data sequence"""
        dual_sequence = self.create_dual_face_sequence()
        analysis = self.enhanced_positioning._analyze_face_data_sequence(dual_sequence)
        
        # Check analysis results
        self.assertTrue(analysis['is_dual_face_scenario'])
        self.assertEqual(analysis['total_frames'], 60)
        self.assertEqual(analysis['face_count_distribution'][2], 60)
        self.assertEqual(analysis['dual_face_ratio'], 1.0)
        self.assertEqual(analysis['recommendation'], 'dual_output')

    def test_processing_recommendations(self):
        """Test processing recommendations generation"""
        dual_sequence = self.create_dual_face_sequence()
        recommendations = self.enhanced_positioning.get_processing_recommendations(dual_sequence)
        
        # Check recommendations structure
        self.assertIn('analysis', recommendations)
        self.assertIn('recommended_approach', recommendations)
        self.assertIn('dual_output_beneficial', recommendations)
        self.assertIn('processing_options', recommendations)
        self.assertIn('expected_benefits', recommendations)
        
        # Check recommendations content
        self.assertEqual(recommendations['recommended_approach'], 'dual_output')
        self.assertTrue(recommendations['dual_output_beneficial'])
        self.assertGreater(len(recommendations['expected_benefits']), 0)

    def test_configuration(self):
        """Test configuration of dual output settings"""
        # Test default configuration
        self.assertTrue(self.enhanced_positioning.enable_dual_output)
        self.assertTrue(self.enhanced_positioning.preserve_single_output)
        self.assertEqual(self.enhanced_positioning.dual_face_threshold, 0.7)
        
        # Test configuration change
        self.enhanced_positioning.configure_dual_output(
            enable=False, preserve_single=False, dual_threshold=0.8
        )
        
        self.assertFalse(self.enhanced_positioning.enable_dual_output)
        self.assertFalse(self.enhanced_positioning.preserve_single_output)
        self.assertEqual(self.enhanced_positioning.dual_face_threshold, 0.8)

    def test_debug_info(self):
        """Test comprehensive debug information"""
        debug_info = self.enhanced_positioning.get_debug_info()
        
        # Check debug info structure
        self.assertIn('enhanced_dual_positioning', debug_info)
        self.assertIn('face_positioning_engine', debug_info)
        self.assertIn('dual_video_compositor', debug_info)
        
        # Check enhanced positioning debug info
        enhanced_info = debug_info['enhanced_dual_positioning']
        self.assertIn('dual_face_threshold', enhanced_info)
        self.assertIn('enable_dual_output', enhanced_info)
        self.assertIn('preserve_single_output', enhanced_info)


if __name__ == '__main__':
    unittest.main()
