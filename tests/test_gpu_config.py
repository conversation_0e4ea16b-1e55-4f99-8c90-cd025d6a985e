#!/usr/bin/env python3
"""
Test script for GPU configuration without MediaPipe initialization

This script tests the GPU detection and configuration logic without
actually initializing MediaPipe, which can hang in headless environments.
"""

import os
import sys
import logging

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_gpu_detection_logic():
    """Test GPU detection logic without initialization"""
    
    print("=" * 60)
    print("GPU Configuration Test")
    print("=" * 60)
    
    # Test environment detection
    print("\n1. Environment Detection:")
    
    # Check CUDA availability
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        print(f"   PyTorch available: True")
        print(f"   CUDA available: {cuda_available}")
        if cuda_available:
            print(f"   CUDA device count: {torch.cuda.device_count()}")
            print(f"   CUDA device name: {torch.cuda.get_device_name(0)}")
    except ImportError:
        cuda_available = False
        print("   PyTorch not available")
    
    # Check display environment
    display = os.environ.get('DISPLAY')
    egl_platform = os.environ.get('EGL_PLATFORM')
    print(f"   DISPLAY environment: {display}")
    print(f"   EGL_PLATFORM environment: {egl_platform}")
    
    opengl_available = bool(display or egl_platform)
    print(f"   OpenGL/EGL available: {opengl_available}")
    
    # Test GPU detection class
    print("\n2. GPU Detection Class Test:")
    
    class MockFaceDetectionEngine:
        def __init__(self, enable_gpu=True):
            self.enable_gpu = enable_gpu
            self.gpu_available = False
            self.using_gpu = False
            self.logger = logger
            self._detect_gpu_availability()
        
        def _detect_gpu_availability(self):
            """Detect GPU availability for MediaPipe and other backends"""
            try:
                # Check for CUDA availability (for InsightFace and general GPU support)
                try:
                    import torch
                    cuda_available = torch.cuda.is_available()
                    if cuda_available:
                        self.logger.info(f"CUDA detected: {torch.cuda.get_device_name(0)}")
                    else:
                        self.logger.info("CUDA not available")
                except ImportError:
                    cuda_available = False
                    self.logger.debug("PyTorch not available for CUDA detection")

                # Check for OpenGL/EGL support (for MediaPipe GPU)
                opengl_available = False
                try:
                    import os
                    # Check if we're in a headless environment
                    display = os.environ.get('DISPLAY')
                    if display or os.environ.get('EGL_PLATFORM'):
                        opengl_available = True
                        self.logger.debug("Display/EGL environment detected")
                    else:
                        self.logger.debug("Headless environment detected")
                except Exception as e:
                    self.logger.debug(f"OpenGL detection failed: {e}")

                # Determine overall GPU availability
                self.gpu_available = (cuda_available or opengl_available) and self.enable_gpu
                
                if self.gpu_available:
                    self.logger.info("GPU acceleration available and enabled")
                else:
                    self.logger.info("GPU acceleration not available or disabled, using CPU")

            except Exception as e:
                self.logger.warning(f"GPU detection failed: {e}, defaulting to CPU")
                self.gpu_available = False
        
        def _configure_mediapipe_environment(self):
            """Configure MediaPipe environment variables for optimal GPU/CPU performance"""
            import os
            
            # Basic logging configuration
            os.environ.setdefault('GLOG_minloglevel', '2')
            os.environ.setdefault('TF_CPP_MIN_LOG_LEVEL', '2')
            
            if self.gpu_available and self.enable_gpu:
                # GPU acceleration configuration
                self.logger.info("Configuring MediaPipe for GPU acceleration")
                
                # Enable GPU delegate
                os.environ.setdefault('MEDIAPIPE_DISABLE_GPU', '0')
                
                # OpenGL/EGL configuration for headless environments
                if not os.environ.get('DISPLAY'):
                    # Headless environment - configure EGL
                    os.environ.setdefault('EGL_PLATFORM', 'surfaceless')
                    os.environ.setdefault('MESA_GL_VERSION_OVERRIDE', '3.3')
                    os.environ.setdefault('MESA_GLSL_VERSION_OVERRIDE', '330')
                    self.logger.debug("Configured EGL for headless GPU acceleration")
                
                # GPU memory and performance settings
                os.environ.setdefault('TF_FORCE_GPU_ALLOW_GROWTH', 'true')
                os.environ.setdefault('TF_GPU_THREAD_MODE', 'gpu_private')
                
            else:
                # CPU-only configuration
                self.logger.info("Configuring MediaPipe for CPU-only execution")
                os.environ['MEDIAPIPE_DISABLE_GPU'] = '1'
                
                # CPU optimization settings
                os.environ.setdefault('TF_NUM_INTEROP_THREADS', '0')  # Use all available cores
                os.environ.setdefault('TF_NUM_INTRAOP_THREADS', '0')  # Use all available cores
        
        def get_gpu_status(self):
            return {
                'gpu_available': self.gpu_available,
                'gpu_enabled': self.enable_gpu,
                'using_gpu': self.using_gpu
            }
    
    # Test with GPU enabled
    print("\n   Testing with GPU enabled:")
    engine_gpu = MockFaceDetectionEngine(enable_gpu=True)
    status = engine_gpu.get_gpu_status()
    print(f"     GPU Available: {status['gpu_available']}")
    print(f"     GPU Enabled: {status['gpu_enabled']}")
    print(f"     Using GPU: {status['using_gpu']}")
    
    # Test environment configuration
    print("\n   Testing environment configuration:")
    engine_gpu._configure_mediapipe_environment()
    print(f"     MEDIAPIPE_DISABLE_GPU: {os.environ.get('MEDIAPIPE_DISABLE_GPU')}")
    print(f"     EGL_PLATFORM: {os.environ.get('EGL_PLATFORM')}")
    
    # Test with GPU disabled
    print("\n   Testing with GPU disabled:")
    engine_cpu = MockFaceDetectionEngine(enable_gpu=False)
    status = engine_cpu.get_gpu_status()
    print(f"     GPU Available: {status['gpu_available']}")
    print(f"     GPU Enabled: {status['gpu_enabled']}")
    print(f"     Using GPU: {status['using_gpu']}")
    
    print("\n" + "=" * 60)
    print("Configuration test completed successfully!")
    print("=" * 60)

if __name__ == "__main__":
    test_gpu_detection_logic()
