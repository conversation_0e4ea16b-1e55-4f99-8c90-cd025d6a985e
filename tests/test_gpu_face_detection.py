#!/usr/bin/env python3
"""
Test script for GPU-accelerated face detection

This script tests the GPU acceleration functionality for both MediaPipe and InsightFace
backends, with proper fallback to CPU when GPU is not available.
"""

import os
import sys
import logging
import numpy as np

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from reframing.face_detection.engine import FaceDetectionEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_gpu_detection():
    """Test GPU-accelerated face detection"""
    
    print("=" * 60)
    print("GPU Face Detection Test")
    print("=" * 60)
    
    # Test MediaPipe with GPU
    print("\n1. Testing MediaPipe with GPU enabled:")
    try:
        engine_mp_gpu = FaceDetectionEngine(backend="mediapipe", enable_gpu=True)
        status = engine_mp_gpu.get_gpu_status()
        print(f"   GPU Available: {status['gpu_available']}")
        print(f"   GPU Enabled: {status['gpu_enabled']}")
        print(f"   Using GPU: {status['using_gpu']}")
        print(f"   Backend: {status['backend']}")
        print(f"   Detector Initialized: {status['detector_initialized']}")
        
        # Test with a sample image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        faces = engine_mp_gpu.detect_faces(test_image)
        print(f"   Test detection completed: {len(faces)} faces detected")
        
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test MediaPipe with CPU only
    print("\n2. Testing MediaPipe with CPU only:")
    try:
        engine_mp_cpu = FaceDetectionEngine(backend="mediapipe", enable_gpu=False)
        status = engine_mp_cpu.get_gpu_status()
        print(f"   GPU Available: {status['gpu_available']}")
        print(f"   GPU Enabled: {status['gpu_enabled']}")
        print(f"   Using GPU: {status['using_gpu']}")
        print(f"   Backend: {status['backend']}")
        print(f"   Detector Initialized: {status['detector_initialized']}")
        
        # Test with a sample image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        faces = engine_mp_cpu.detect_faces(test_image)
        print(f"   Test detection completed: {len(faces)} faces detected")
        
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test InsightFace with GPU
    print("\n3. Testing InsightFace with GPU enabled:")
    try:
        engine_if_gpu = FaceDetectionEngine(backend="insightface", enable_gpu=True)
        status = engine_if_gpu.get_gpu_status()
        print(f"   GPU Available: {status['gpu_available']}")
        print(f"   GPU Enabled: {status['gpu_enabled']}")
        print(f"   Using GPU: {status['using_gpu']}")
        print(f"   Backend: {status['backend']}")
        print(f"   Detector Initialized: {status['detector_initialized']}")
        
        # Test with a sample image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        faces = engine_if_gpu.detect_faces(test_image)
        print(f"   Test detection completed: {len(faces)} faces detected")
        
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test auto-selection
    print("\n4. Testing auto backend selection:")
    try:
        engine_auto = FaceDetectionEngine(backend="auto", enable_gpu=True)
        status = engine_auto.get_gpu_status()
        print(f"   Selected Backend: {status['backend']}")
        print(f"   GPU Available: {status['gpu_available']}")
        print(f"   Using GPU: {status['using_gpu']}")
        print(f"   Detector Initialized: {status['detector_initialized']}")
        
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("=" * 60)

def test_environment_detection():
    """Test environment detection capabilities"""
    
    print("\n" + "=" * 60)
    print("Environment Detection Test")
    print("=" * 60)
    
    # Check CUDA availability
    try:
        import torch
        print(f"PyTorch available: True")
        print(f"CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"CUDA device count: {torch.cuda.device_count()}")
            print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    except ImportError:
        print("PyTorch not available")
    
    # Check display environment
    display = os.environ.get('DISPLAY')
    egl_platform = os.environ.get('EGL_PLATFORM')
    print(f"DISPLAY environment: {display}")
    print(f"EGL_PLATFORM environment: {egl_platform}")
    
    # Check MediaPipe environment variables
    mp_disable_gpu = os.environ.get('MEDIAPIPE_DISABLE_GPU')
    print(f"MEDIAPIPE_DISABLE_GPU: {mp_disable_gpu}")

if __name__ == "__main__":
    test_environment_detection()
    test_gpu_detection()
