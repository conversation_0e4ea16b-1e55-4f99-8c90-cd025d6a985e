#!/usr/bin/env python3
"""
Simple test for Hindi language support
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("Starting simple test...")
    
    try:
        print("Importing VideoHighlightsDetector...")
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector
        print("✓ Import successful")
        
        print("Creating detector...")
        detector = VideoHighlightsDetector(language="auto")
        print("✓ Detector created")
        
        print("Testing language detection...")
        hindi_segments = [
            {"text": "नमस्कार दोस्तों! यह अविश्वसनीय है।", "start": 0.0, "end": 3.0}
        ]
        
        detector._detect_content_language(hindi_segments)
        print(f"✓ Language detected: {detector.detected_language} (confidence: {detector.language_confidence:.2f})")
        
        print("Testing Hindi sentence splitting...")
        detector.detected_language = "hindi"
        hindi_text = "यह पहला वाक्य है। यह दूसरा वाक्य है॥"
        sentences = detector._hindi_sentence_splitting(hindi_text)
        print(f"✓ Sentences found: {len(sentences)}")
        
        print("Testing feature weights...")
        hindi_detector = VideoHighlightsDetector(language="hindi")
        print(f"✓ Hindi emotion weight: {hindi_detector.feature_weights['emotion']:.3f}")
        
        print("\n🎉 All basic tests passed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
