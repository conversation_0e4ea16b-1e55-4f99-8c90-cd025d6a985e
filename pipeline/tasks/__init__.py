"""
Pipeline tasks for the Smart Video Highlight Generator
"""

from pipeline.tasks.preflight_validator import validate_video
from pipeline.tasks.video_ingestor import ingest_video
from pipeline.tasks.transcription_engine import transcribe_video
from pipeline.tasks.world_class_highlights_extractor import AdvancedHighlightsExtractor
from pipeline.tasks.clip_renderer import render_clips
from pipeline.tasks.reframer import reframe_clips
from pipeline.tasks.caption_composer import compose_captions
from pipeline.tasks.deliverables_publisher import publish_deliverables
from pipeline.tasks.cleanup_metrics import cleanup_and_metrics
