#!/usr/bin/env python3
"""
Comprehensive test for Hindi language support
"""

import sys
import os
import time

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_language_detection():
    """Test language detection accuracy"""
    print("\n1. LANGUAGE DETECTION TEST")
    print("-" * 40)
    
    from highlight_extraction.utils.video_detector import VideoHighlightsDetector
    
    # Test cases
    test_cases = [
        {
            "name": "Pure Hindi",
            "segments": [{"text": "नमस्कार दोस्तों! आज मैं आपको एक अविश्वसनीय कहानी सुनाने जा रहा हूँ।", "start": 0.0, "end": 4.0}],
            "expected": "hindi"
        },
        {
            "name": "Pure English", 
            "segments": [{"text": "Hello friends! Today I'm going to tell you an incredible story.", "start": 0.0, "end": 4.0}],
            "expected": "english"
        },
        {
            "name": "Mixed Content",
            "segments": [{"text": "Hello दोस्तों! This is बहुत interesting.", "start": 0.0, "end": 3.0}],
            "expected": "english"  # Should default to English for mixed
        }
    ]
    
    results = []
    for test_case in test_cases:
        detector = VideoHighlightsDetector(language="auto")
        detector._detect_content_language(test_case["segments"])
        
        success = detector.detected_language == test_case["expected"]
        results.append(success)
        
        status = "✓" if success else "✗"
        print(f"   {status} {test_case['name']}: {detector.detected_language} (confidence: {detector.language_confidence:.2f})")
    
    return all(results)

def test_text_processing():
    """Test Hindi text processing features"""
    print("\n2. TEXT PROCESSING TEST")
    print("-" * 40)
    
    from highlight_extraction.utils.video_detector import VideoHighlightsDetector
    
    detector = VideoHighlightsDetector(language="hindi")
    detector.detected_language = "hindi"
    
    # Test Hindi sentence splitting
    test_texts = [
        "यह पहला वाक्य है। यह दूसरा वाक्य है॥ यह तीसरा है।",
        "क्या आप जानते हैं? यह बहुत महत्वपूर्ण है।",
        "वाह! यह अद्भुत है॥ सच में चौंकाने वाला।"
    ]
    
    results = []
    for i, text in enumerate(test_texts):
        sentences = detector._hindi_sentence_splitting(text)
        success = len(sentences) >= 2  # Should split into multiple sentences
        results.append(success)
        
        status = "✓" if success else "✗"
        print(f"   {status} Text {i+1}: {len(sentences)} sentences found")
        print(f"      Input: {text}")
        print(f"      Output: {sentences}")
    
    return all(results)

def test_engagement_patterns():
    """Test Hindi engagement pattern recognition"""
    print("\n3. ENGAGEMENT PATTERNS TEST")
    print("-" * 40)
    
    from highlight_extraction.utils.video_detector import VideoHighlightsDetector
    
    detector = VideoHighlightsDetector(language="hindi")
    detector.detected_language = "hindi"
    
    # Test Hindi engagement patterns
    test_segments = [
        {
            "text": "क्या होगा अगर मैं आपको बताऊं कि यह अविश्वसनीय रहस्य है!",
            "start": 0.0, "end": 4.0,
            "expected_high": True  # Should have high engagement
        },
        {
            "text": "अनुसंधान दिखाता है कि यह सच्चाई है। वाकई चौंकाने वाला।",
            "start": 4.0, "end": 8.0,
            "expected_high": True
        },
        {
            "text": "यह एक सामान्य वाक्य है।",
            "start": 8.0, "end": 10.0,
            "expected_high": False  # Should have lower engagement
        }
    ]
    
    scores = detector._analyze_segment_engagement(test_segments)
    
    results = []
    for i, segment in enumerate(test_segments):
        score = scores[i]
        expected_high = segment["expected_high"]
        
        if expected_high:
            success = score > 0.2  # High engagement threshold
        else:
            success = score <= 0.2  # Low engagement threshold
        
        results.append(success)
        status = "✓" if success else "✗"
        print(f"   {status} Segment {i+1}: score={score:.3f} (expected {'high' if expected_high else 'low'})")
        print(f"      Text: {segment['text']}")
    
    return all(results)

def test_feature_weights():
    """Test language-specific feature weights"""
    print("\n4. FEATURE WEIGHTS TEST")
    print("-" * 40)
    
    from highlight_extraction.utils.video_detector import VideoHighlightsDetector
    
    hindi_detector = VideoHighlightsDetector(language="hindi")
    english_detector = VideoHighlightsDetector(language="english")
    
    # Check that Hindi has higher emotion weight
    hindi_emotion = hindi_detector.feature_weights['emotion']
    english_emotion = english_detector.feature_weights['emotion']
    
    # Check that Hindi has lower burstiness weight
    hindi_burstiness = hindi_detector.feature_weights['burstiness']
    english_burstiness = english_detector.feature_weights['burstiness']
    
    # Verify weights sum to 1.0
    hindi_sum = sum(hindi_detector.feature_weights.values())
    english_sum = sum(english_detector.feature_weights.values())
    
    results = [
        hindi_emotion > english_emotion,  # Hindi should have higher emotion weight
        hindi_burstiness < english_burstiness,  # Hindi should have lower burstiness weight
        abs(hindi_sum - 1.0) < 0.001,  # Weights should sum to 1.0
        abs(english_sum - 1.0) < 0.001
    ]
    
    print(f"   Hindi emotion weight: {hindi_emotion:.3f} vs English: {english_emotion:.3f}")
    print(f"   Hindi burstiness weight: {hindi_burstiness:.3f} vs English: {english_burstiness:.3f}")
    print(f"   Hindi weights sum: {hindi_sum:.3f}")
    print(f"   English weights sum: {english_sum:.3f}")
    
    status = "✓" if all(results) else "✗"
    print(f"   {status} Weight adjustments are correct")
    
    return all(results)

def test_burstiness_analysis():
    """Test language-aware burstiness analysis"""
    print("\n5. BURSTINESS ANALYSIS TEST")
    print("-" * 40)
    
    from highlight_extraction.utils.video_detector import VideoHighlightsDetector
    
    # Test Hindi burstiness
    hindi_detector = VideoHighlightsDetector(language="hindi")
    hindi_detector.detected_language = "hindi"
    
    hindi_text = "वाह! यह बहुत अद्भुत है। वाकई चौंकाने वाला॥"
    hindi_burstiness = hindi_detector._extract_burstiness_features(hindi_text)
    
    # Test English burstiness
    english_detector = VideoHighlightsDetector(language="english")
    english_detector.detected_language = "english"
    
    english_text = "Wow! This is really amazing. Absolutely shocking!"
    english_burstiness = english_detector._extract_burstiness_features(english_text)
    
    print(f"   Hindi burstiness: {hindi_burstiness['overall_burstiness']:.3f}")
    print(f"   English burstiness: {english_burstiness['overall_burstiness']:.3f}")
    print(f"   Hindi punctuation density: {hindi_burstiness['punctuation_density']:.3f}")
    print(f"   English punctuation density: {english_burstiness['punctuation_density']:.3f}")
    
    # Both should have reasonable burstiness scores
    results = [
        hindi_burstiness['overall_burstiness'] > 0.1,
        english_burstiness['overall_burstiness'] > 0.1,
        hindi_burstiness['punctuation_density'] > 0,
        english_burstiness['punctuation_density'] > 0
    ]
    
    status = "✓" if all(results) else "✗"
    print(f"   {status} Burstiness analysis working correctly")
    
    return all(results)

def test_emotion_detection():
    """Test fallback emotion detection"""
    print("\n6. EMOTION DETECTION TEST")
    print("-" * 40)
    
    from highlight_extraction.utils.video_detector import VideoHighlightsDetector
    
    # Test Hindi emotions
    hindi_detector = VideoHighlightsDetector(language="hindi")
    hindi_detector.detected_language = "hindi"
    
    hindi_emotional_text = "यह अविश्वसनीय है! मैं बहुत खुश हूँ। वाकई अद्भुत।"
    hindi_emotions = hindi_detector._fallback_emotion_detection(hindi_emotional_text)
    
    # Test English emotions
    english_detector = VideoHighlightsDetector(language="english")
    english_detector.detected_language = "english"
    
    english_emotional_text = "This is incredible! I am very happy. Really amazing."
    english_emotions = english_detector._fallback_emotion_detection(english_emotional_text)
    
    print(f"   Hindi emotion intensity: {hindi_emotions['total_intensity']:.3f}")
    print(f"   English emotion intensity: {english_emotions['total_intensity']:.3f}")
    
    # Both should detect some emotions
    results = [
        hindi_emotions['total_intensity'] > 0.1,
        english_emotions['total_intensity'] > 0.1
    ]
    
    status = "✓" if all(results) else "✗"
    print(f"   {status} Emotion detection working correctly")
    
    return all(results)

def main():
    """Run all tests"""
    print("COMPREHENSIVE HINDI LANGUAGE SUPPORT TEST")
    print("=" * 60)
    
    test_functions = [
        ("Language Detection", test_language_detection),
        ("Text Processing", test_text_processing),
        ("Engagement Patterns", test_engagement_patterns),
        ("Feature Weights", test_feature_weights),
        ("Burstiness Analysis", test_burstiness_analysis),
        ("Emotion Detection", test_emotion_detection)
    ]
    
    results = {}
    start_time = time.time()
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results[test_name] = "PASSED" if result else "FAILED"
        except Exception as e:
            results[test_name] = f"ERROR: {str(e)}"
            print(f"   ❌ Error in {test_name}: {e}")
    
    total_time = time.time() - start_time
    
    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        if result == "PASSED":
            print(f"✓ {test_name}: {result}")
            passed += 1
        else:
            print(f"✗ {test_name}: {result}")
    
    print(f"\nResults: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    print(f"Total time: {total_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Hindi language support is working correctly.")
    else:
        print(f"\n⚠️  {total-passed} test(s) failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
