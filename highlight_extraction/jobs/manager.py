#!/usr/bin/env python3
"""
Job manager for highlights extraction jobs
"""

import os
import uuid
import time
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, Future

from .models import HighlightsJob, HighlightsJobInput, JobStatus
from .executor import High<PERSON>JobExecutor
from ..config.settings import (
    HIGHLIGHTS_OUTPUT_DIR, 
    HIGHLIGHTS_LOGS_DIR,
    MAX_CONCURRENT_JOBS,
    JOB_CLEANUP_DAYS,
    LOG_LEVEL,
    LOG_FORMAT
)


class HighlightsJobManager:
    """
    Manager for highlights extraction jobs
    
    Handles job submission, execution, monitoring, and cleanup.
    Supports concurrent job execution with configurable limits.
    """

    def __init__(self):
        """Initialize the job manager"""
        self.jobs: Dict[str, HighlightsJob] = {}
        self.futures: Dict[str, Future] = {}
        self.executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_JOBS)
        self.job_executor = HighlightsJobExecutor()
        self._lock = threading.Lock()
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Create output directories
        self.output_dir = Path(HIGHLIGHTS_OUTPUT_DIR)
        self.logs_dir = Path(HIGHLIGHTS_LOGS_DIR)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Load existing jobs
        self._load_existing_jobs()
        
        self.logger.info(f"HighlightsJobManager initialized with {MAX_CONCURRENT_JOBS} max concurrent jobs")

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the job manager"""
        logger = logging.getLogger('highlights_job_manager')
        logger.setLevel(getattr(logging, LOG_LEVEL))
        
        # Create file handler
        log_file = Path(HIGHLIGHTS_LOGS_DIR) / 'job_manager.log'
        handler = logging.FileHandler(log_file)
        handler.setLevel(getattr(logging, LOG_LEVEL))
        
        # Create formatter
        formatter = logging.Formatter(LOG_FORMAT)
        handler.setFormatter(formatter)
        
        # Add handler to logger
        if not logger.handlers:
            logger.addHandler(handler)
        
        return logger

    def _load_existing_jobs(self):
        """Load existing jobs from disk"""
        try:
            for job_dir in self.output_dir.iterdir():
                if job_dir.is_dir():
                    job_file = job_dir / 'job.json'
                    if job_file.exists():
                        try:
                            job = HighlightsJob.load_from_file(job_file)
                            self.jobs[job.job_id] = job
                            self.logger.debug(f"Loaded existing job: {job.job_id}")
                        except Exception as e:
                            self.logger.warning(f"Failed to load job from {job_file}: {e}")
        except Exception as e:
            self.logger.warning(f"Failed to load existing jobs: {e}")

    def submit_job(self,
                   transcription_data: Dict[str, Any],
                   video_path: str,
                   keywords: List[str],
                   target_length: int = 75,
                   extract_segments: bool = True,
                   params: Optional[Dict[str, Any]] = None) -> str:
        """
        Submit a new highlights extraction job

        Args:
            transcription_data: Transcription data with segments
            video_path: Path to the video file
            keywords: Keywords to search for
            target_length: Target total duration in seconds
            extract_segments: Whether to extract individual segments
            params: Additional parameters

        Returns:
            Job ID for tracking the job
        """
        job_id = str(uuid.uuid4())
        
        # Create job input
        job_input = HighlightsJobInput(
            transcription_data=transcription_data,
            video_path=video_path,
            keywords=keywords,
            target_length=target_length,
            extract_segments=extract_segments,
            params=params or {}
        )
        
        # Create job
        job = HighlightsJob(
            job_id=job_id,
            input_data=job_input,
            status=JobStatus.PENDING,
            created_at=time.time()
        )
        
        # Create job directory
        job_dir = self.output_dir / job_id
        job_dir.mkdir(exist_ok=True)
        
        # Save job
        with self._lock:
            self.jobs[job_id] = job
            job.save_to_file(job_dir / 'job.json')
        
        # Submit for execution
        future = self.executor.submit(self._execute_job, job_id)
        self.futures[job_id] = future
        
        self.logger.info(f"Submitted highlights extraction job: {job_id}")
        return job_id

    def _execute_job(self, job_id: str):
        """Execute a highlights extraction job"""
        try:
            with self._lock:
                job = self.jobs.get(job_id)
                if not job:
                    self.logger.error(f"Job not found: {job_id}")
                    return
                
                job.start()
                job_dir = self.output_dir / job_id
                job.save_to_file(job_dir / 'job.json')
            
            self.logger.info(f"Starting execution of job: {job_id}")
            
            # Execute the job
            result = self.job_executor.execute(job)
            
            with self._lock:
                job.complete(result)
                job.save_to_file(job_dir / 'job.json')
            
            self.logger.info(f"Completed job: {job_id} in {job.duration:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Job execution failed: {job_id} - {e}")
            with self._lock:
                job = self.jobs.get(job_id)
                if job:
                    job.fail(str(e))
                    job_dir = self.output_dir / job_id
                    job.save_to_file(job_dir / 'job.json')
        
        finally:
            # Clean up future
            if job_id in self.futures:
                del self.futures[job_id]

    def get_job(self, job_id: str) -> Optional[HighlightsJob]:
        """Get job by ID"""
        with self._lock:
            return self.jobs.get(job_id)

    def get_job_status(self, job_id: str) -> Optional[JobStatus]:
        """Get job status by ID"""
        job = self.get_job(job_id)
        return job.status if job else None

    def get_job_result(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job result by ID"""
        job = self.get_job(job_id)
        if job and job.result:
            return job.result.to_dict()
        return None

    def list_jobs(self, status: Optional[JobStatus] = None) -> List[Dict[str, Any]]:
        """List all jobs, optionally filtered by status"""
        with self._lock:
            jobs = list(self.jobs.values())
        
        if status:
            jobs = [job for job in jobs if job.status == status]
        
        return [job.to_dict() for job in jobs]

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job"""
        with self._lock:
            job = self.jobs.get(job_id)
            if not job:
                return False
            
            if job.status in [JobStatus.PENDING, JobStatus.RUNNING]:
                job.cancel()
                job_dir = self.output_dir / job_id
                job.save_to_file(job_dir / 'job.json')
                
                # Cancel future if it exists
                future = self.futures.get(job_id)
                if future:
                    future.cancel()
                
                self.logger.info(f"Cancelled job: {job_id}")
                return True
        
        return False

    def cleanup_old_jobs(self, days: int = JOB_CLEANUP_DAYS):
        """Clean up old completed jobs"""
        cutoff_time = time.time() - (days * 24 * 3600)
        cleaned_count = 0
        
        with self._lock:
            jobs_to_remove = []
            for job_id, job in self.jobs.items():
                if (job.is_finished and 
                    job.completed_at and 
                    job.completed_at < cutoff_time):
                    jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                try:
                    # Remove job directory
                    job_dir = self.output_dir / job_id
                    if job_dir.exists():
                        import shutil
                        shutil.rmtree(job_dir)
                    
                    # Remove from memory
                    del self.jobs[job_id]
                    cleaned_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to cleanup job {job_id}: {e}")
        
        if cleaned_count > 0:
            self.logger.info(f"Cleaned up {cleaned_count} old jobs")
        
        return cleaned_count

    def shutdown(self):
        """Shutdown the job manager"""
        self.logger.info("Shutting down HighlightsJobManager")
        self.executor.shutdown(wait=True)
