#!/usr/bin/env python3
"""
Intelligent Video Highlights Extractor Task

Pipeline task wrapper for the intelligent highlights extraction functionality.
This task integrates with the main video processing pipeline and uses the
core IntelligentExtractor for the actual highlight extraction logic.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional

import sys
import os
# Add the project root to the path to import BaseTask
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from pipeline.tasks.base_task import BaseTask
from config.settings import (
    PIPELINE_OUTPUT_DIR,
    OPENAI_HIGHLIGHTS_ENABLED,
    OPENAI_HIGHLIGHTS_MODEL,
    INTELLIGENT_HIGHLIGHTS_MIN_SPAN,
    INTELLIGENT_HIGHLIGHTS_MAX_SPAN,
    OPENAI_HIGHLIGHTS_MAX_REQUESTS,
    OPENAI_HIGHLIGHTS_TOP_CANDIDATES,
)

# Import the core extractor from the new highlight extraction module
from ..core.intelligent_extractor import IntelligentExtractor
from ..utils.iframe_utils import IFrameExtractor


class IntelligentHighlightsTask(BaseTask):
    """
    Pipeline task wrapper for intelligent highlights extraction

    This task provides the pipeline interface while delegating the actual
    extraction logic to the core IntelligentExtractor class.
    """

    task_name = "intelligent_highlights_extractor"
    requires_gpu = True  # For ML models (sentiment analysis, embeddings)

    def __init__(self):
        super().__init__()
        self.default_target_length = 75.0  # Default target highlight length in seconds

        # Initialize the core extractor
        self.extractor = IntelligentExtractor()
        self.iframe_extractor = IFrameExtractor()

    def run(self, job_id: str, transcription_result: Dict[str, Any],
            video_ingestor_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract intelligent highlights from video transcription

        Args:
            job_id: Unique identifier for the job
            transcription_result: Results from transcription engine
            video_ingestor_result: Results from video ingestor (for video path)
            params: Additional parameters including keywords and target length

        Returns:
            Task result with highlights metadata and file paths
        """
        start_time = time.time()

        try:
            self.logger.info(f"Starting intelligent highlights extraction for job {job_id}")

            # Create output directories
            job_dir = os.path.join(PIPELINE_OUTPUT_DIR, job_id)
            highlights_dir = os.path.join(job_dir, "intelligent_highlights")
            os.makedirs(highlights_dir, exist_ok=True)

            # Get parameters
            keywords = params.get('keywords', [])
            target_length = params.get('target_length', self.default_target_length)

            self.logger.info(f"Target keywords: {keywords}")
            self.logger.info(f"Target highlight length: {target_length}s")

            # Load transcript data
            transcript_path = transcription_result.get('transcript_path')
            if not transcript_path or not os.path.exists(transcript_path):
                raise ValueError(f"Invalid transcript path: {transcript_path}")

            with open(transcript_path, 'r') as f:
                transcript_data = json.load(f)

            # Get video path for I-frame extraction
            video_path = video_ingestor_result.get('optimized_path') or video_ingestor_result.get('video_path')
            if not video_path or not os.path.exists(video_path):
                raise ValueError(f"Invalid video path: {video_path}")

            # Use the core extractor to perform the actual extraction
            extraction_result = self.extractor.extract(
                transcription_data=transcript_data,
                video_path=video_path,
                keywords=keywords,
                target_length=target_length,
                output_dir=highlights_dir,
                params=params
            )

            # Generate output files
            outputs = self._generate_outputs(
                extraction_result['highlights'],
                video_path,
                highlights_dir,
                job_id
            )

            # Prepare pipeline result
            result = {
                'status': 'completed',
                'highlights_count': extraction_result['final_highlights_count'],
                'total_duration': extraction_result['total_duration'],
                'target_duration': target_length,
                'highlights_path': outputs['highlights_json'],
                'concat_file_path': outputs['concat_file'],
                'execution_time': extraction_result['execution_time'],
                'metadata': {
                    'keywords': keywords,
                    'qa_first_approach': extraction_result.get('qa_first_approach', False),
                    'complete_qa_pairs_found': extraction_result.get('complete_qa_pairs_found', 0),
                    'question_segments_found': extraction_result.get('question_segments_found', 0),
                    'spans_generated': extraction_result.get('spans_generated', 0),
                    'spans_after_filtering': extraction_result.get('spans_after_filtering', 0),
                    'final_highlights_count': extraction_result['final_highlights_count']
                }
            }

            # Save state
            self.save_state(job_id, result, self.task_name)

            self.logger.info(f"Intelligent highlights extraction completed in {extraction_result['execution_time']:.2f}s")
            self.logger.info(f"Generated {extraction_result['final_highlights_count']} highlights with total duration {extraction_result['total_duration']:.1f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error in intelligent highlights extraction: {str(e)}")
            error_result = {
                'status': 'failed',
                'error': str(e),
                'execution_time': time.time() - start_time
            }
            self.save_state(job_id, error_result, self.task_name)
            return error_result

    def _generate_outputs(self, highlights: List[Dict[str, Any]], video_path: str,
                         highlights_dir: str, job_id: str) -> Dict[str, str]:
        """
        Generate output files for the highlights

        Args:
            highlights: List of highlight segments
            video_path: Path to source video
            highlights_dir: Output directory
            job_id: Job identifier

        Returns:
            Dictionary with output file paths
        """
        # Generate highlights JSON
        highlights_json_path = os.path.join(highlights_dir, f"{job_id}_highlights.json")
        with open(highlights_json_path, 'w') as f:
            json.dump({
                'highlights': highlights,
                'total_duration': sum(h['duration'] for h in highlights),
                'count': len(highlights),
                'generated_at': time.time()
            }, f, indent=2)

        # Generate FFmpeg concat file
        concat_file_path = os.path.join(highlights_dir, f"{job_id}_concat.txt")
        concat_file = self.iframe_extractor.generate_ffmpeg_concat_file(
            highlights, video_path, concat_file_path
        )

        return {
            'highlights_json': highlights_json_path,
            'concat_file': concat_file
        }
