#!/usr/bin/env python3
"""
I-Frame Extraction Utility for Intelligent Video Highlights

This module provides functionality to extract I-frame timestamps from video files
and snap highlight boundaries to the nearest I-frames for lossless video cutting.
"""

import os
import json
import logging
import subprocess
from typing import List, Dict, Any, Tuple, Optional
import ffmpeg

logger = logging.getLogger(__name__)


class IFrameExtractor:
    """
    Utility class for extracting I-frame timestamps and snapping boundaries
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def extract_iframe_timestamps(self, video_path: str) -> List[float]:
        """
        Extract all I-frame timestamps from a video file using ffprobe
        
        Args:
            video_path: Path to the video file
            
        Returns:
            List of I-frame timestamps in seconds
        """
        try:
            # Use ffprobe to get I-frame information
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-select_streams', 'v:0',
                '-show_entries', 'frame=pkt_pts_time',
                '-of', 'csv=p=0',
                '-skip_frame', 'nokey',  # Only show keyframes (I-frames)
                video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Parse timestamps
            timestamps = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        timestamp = float(line.strip())
                        timestamps.append(timestamp)
                    except ValueError:
                        continue
            
            # Sort timestamps to ensure they're in order
            timestamps.sort()
            
            self.logger.info(f"Extracted {len(timestamps)} I-frame timestamps from {video_path}")
            return timestamps
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error extracting I-frames from {video_path}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error extracting I-frames: {e}")
            return []
    
    def find_nearest_iframe(self, target_time: float, iframe_timestamps: List[float], 
                           direction: str = 'nearest') -> float:
        """
        Find the nearest I-frame timestamp to a target time
        
        Args:
            target_time: Target timestamp in seconds
            iframe_timestamps: List of I-frame timestamps
            direction: 'nearest', 'before', or 'after'
            
        Returns:
            Nearest I-frame timestamp
        """
        if not iframe_timestamps:
            return target_time
        
        if direction == 'before':
            # Find the latest I-frame before or at the target time
            candidates = [t for t in iframe_timestamps if t <= target_time]
            return max(candidates) if candidates else iframe_timestamps[0]
        
        elif direction == 'after':
            # Find the earliest I-frame after or at the target time
            candidates = [t for t in iframe_timestamps if t >= target_time]
            return min(candidates) if candidates else iframe_timestamps[-1]
        
        else:  # direction == 'nearest'
            # Find the closest I-frame in either direction
            return min(iframe_timestamps, key=lambda x: abs(x - target_time))
    
    def snap_highlights_to_iframes(self, highlights: List[Dict[str, Any]], 
                                  iframe_timestamps: List[float]) -> List[Dict[str, Any]]:
        """
        Snap highlight boundaries to nearest I-frames for lossless cutting
        
        Args:
            highlights: List of highlight dictionaries with start_time and end_time
            iframe_timestamps: List of I-frame timestamps
            
        Returns:
            Updated highlights with snapped boundaries
        """
        if not iframe_timestamps:
            self.logger.warning("No I-frame timestamps available, returning original highlights")
            return highlights
        
        snapped_highlights = []
        
        for highlight in highlights:
            original_start = highlight['start_time']
            original_end = highlight['end_time']
            
            # Snap start time to nearest earlier I-frame
            snapped_start = self.find_nearest_iframe(original_start, iframe_timestamps, 'before')
            
            # Snap end time to nearest later I-frame
            snapped_end = self.find_nearest_iframe(original_end, iframe_timestamps, 'after')
            
            # Create updated highlight
            snapped_highlight = highlight.copy()
            snapped_highlight['start_time'] = snapped_start
            snapped_highlight['end_time'] = snapped_end
            snapped_highlight['duration'] = snapped_end - snapped_start
            
            # Store original times for reference
            snapped_highlight['original_start_time'] = original_start
            snapped_highlight['original_end_time'] = original_end
            snapped_highlight['original_duration'] = original_end - original_start
            
            # Calculate adjustment amounts
            start_adjustment = snapped_start - original_start
            end_adjustment = snapped_end - original_end
            snapped_highlight['start_adjustment'] = start_adjustment
            snapped_highlight['end_adjustment'] = end_adjustment
            
            snapped_highlights.append(snapped_highlight)
            
            self.logger.debug(f"Snapped highlight: {original_start:.2f}-{original_end:.2f} -> "
                            f"{snapped_start:.2f}-{snapped_end:.2f}")
        
        self.logger.info(f"Snapped {len(snapped_highlights)} highlights to I-frame boundaries")
        return snapped_highlights
    
    def generate_ffmpeg_concat_file(self, highlights: List[Dict[str, Any]], 
                                   video_path: str, output_path: str) -> str:
        """
        Generate FFmpeg concat demuxer file for lossless video stitching
        
        Args:
            highlights: List of highlight dictionaries with snapped boundaries
            video_path: Path to source video file
            output_path: Path where concat file should be saved
            
        Returns:
            Path to the generated concat file
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w') as f:
                f.write("# FFmpeg concat demuxer file for highlights\n")
                f.write("# Generated by Intelligent Video Highlights Extractor\n")
                f.write("ffconcat version 1.0\n\n")
                
                for i, highlight in enumerate(highlights):
                    start_time = highlight['start_time']
                    duration = highlight['duration']
                    
                    f.write(f"# Highlight {i+1}: {start_time:.3f}s - {start_time + duration:.3f}s\n")
                    f.write(f"file '{os.path.abspath(video_path)}'\n")
                    f.write(f"inpoint {start_time:.6f}\n")
                    f.write(f"outpoint {start_time + duration:.6f}\n\n")
            
            self.logger.info(f"Generated FFmpeg concat file: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error generating concat file: {e}")
            raise
    
    def validate_iframe_cuts(self, highlights: List[Dict[str, Any]], 
                           iframe_timestamps: List[float]) -> bool:
        """
        Validate that all highlight boundaries align with I-frames
        
        Args:
            highlights: List of highlight dictionaries
            iframe_timestamps: List of I-frame timestamps
            
        Returns:
            True if all boundaries are valid, False otherwise
        """
        if not iframe_timestamps:
            return False
        
        iframe_set = set(iframe_timestamps)
        
        for highlight in highlights:
            start_time = highlight['start_time']
            end_time = highlight['end_time']
            
            # Check if start and end times are I-frames (with small tolerance)
            start_valid = any(abs(start_time - iframe) < 0.001 for iframe in iframe_timestamps)
            end_valid = any(abs(end_time - iframe) < 0.001 for iframe in iframe_timestamps)
            
            if not (start_valid and end_valid):
                self.logger.warning(f"Invalid I-frame alignment for highlight {start_time}-{end_time}")
                return False
        
        return True


# Create a global instance for easy access
iframe_extractor = IFrameExtractor()
