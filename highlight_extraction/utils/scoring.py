#!/usr/bin/env python3
"""
GPU-Accelerated Advanced Highlights Scoring Utility for Intelligent Video Highlights

This module implements a sophisticated, self-contained scoring system for video highlights:
- Enhanced Q&A Detection with semantic analysis (35%)
- Advanced Keyword Density using KeyBERT and semantic similarity (25%)
- Multi-model Emotion Intensity via sentiment analysis and engagement detection (25%)
- Improved Novelty scoring with topic diversity analysis (15%)

Key improvements:
- GPU acceleration with automatic CPU fallback for optimal performance
- Enhanced accuracy through ensemble methods and sophisticated embeddings
- Batch processing for efficient GPU utilization
- Advanced caching and memory management
- Performance monitoring and metrics
- Backward compatibility with existing interfaces
"""

import logging
import numpy as np
import time
import gc
from typing import List, Dict, Any, Optional, Tuple, Union
import warnings
from collections import defaultdict

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore")

logger = logging.getLogger(__name__)

# Check for library availability without importing heavy dependencies
KEYBERT_AVAILABLE = False
TRANSFORMERS_AVAILABLE = False
SENTENCE_TRANSFORMERS_AVAILABLE = False
SKLEARN_AVAILABLE = False
SCIPY_AVAILABLE = False
TORCH_AVAILABLE = False
CUDA_AVAILABLE = False

try:
    import importlib.util

    # Check PyTorch availability for GPU support
    if importlib.util.find_spec("torch") is not None:
        TORCH_AVAILABLE = True
        import torch
        CUDA_AVAILABLE = torch.cuda.is_available()

    # Check KeyBERT availability
    if importlib.util.find_spec("keybert") is not None:
        KEYBERT_AVAILABLE = True

    # Check Transformers availability
    if importlib.util.find_spec("transformers") is not None:
        TRANSFORMERS_AVAILABLE = True

    # Check SentenceTransformers availability
    if importlib.util.find_spec("sentence_transformers") is not None:
        SENTENCE_TRANSFORMERS_AVAILABLE = True

    # Check sklearn availability
    if importlib.util.find_spec("sklearn") is not None:
        SKLEARN_AVAILABLE = True

    # Check scipy availability
    if importlib.util.find_spec("scipy") is not None:
        SCIPY_AVAILABLE = True

except Exception as e:
    logger.warning(f"Error checking library availability: {e}")

# GPU Configuration
class GPUConfig:
    """GPU configuration and management"""
    def __init__(self):
        self.device = self._detect_device()
        self.memory_limit_gb = 4.0  # Default GPU memory limit
        self.batch_size = 32  # Default batch size for GPU processing
        self.enable_gpu = True  # Can be overridden by settings

    def _detect_device(self) -> str:
        """Detect optimal device for computation"""
        if TORCH_AVAILABLE and CUDA_AVAILABLE:
            return "cuda"
        elif TORCH_AVAILABLE:
            # Check for MPS (Apple Silicon) support
            try:
                import torch
                if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    return "mps"
            except ImportError:
                pass
        return "cpu"

    def get_device(self) -> str:
        """Get the current device"""
        return self.device if self.enable_gpu else "cpu"

    def is_gpu_available(self) -> bool:
        """Check if GPU is available and enabled"""
        return self.enable_gpu and self.device in ["cuda", "mps"]

# Global GPU configuration
gpu_config = GPUConfig()

# Log availability status
logger.info(f"Library availability - KeyBERT: {KEYBERT_AVAILABLE}, Transformers: {TRANSFORMERS_AVAILABLE}, "
           f"SentenceTransformers: {SENTENCE_TRANSFORMERS_AVAILABLE}, sklearn: {SKLEARN_AVAILABLE}, "
           f"scipy: {SCIPY_AVAILABLE}")
logger.info(f"GPU availability - PyTorch: {TORCH_AVAILABLE}, CUDA: {CUDA_AVAILABLE}, "
           f"Device: {gpu_config.device}, GPU Enabled: {gpu_config.enable_gpu}")


class HighlightsScorer:
    """
    GPU-Accelerated Advanced scoring system for video highlights extraction

    This system uses multiple ML models with GPU acceleration and sophisticated analysis
    techniques to identify the most engaging video segments without relying on external APIs.

    Features:
    - GPU acceleration with automatic CPU fallback
    - Batch processing for optimal GPU utilization
    - Advanced caching and memory management
    - Performance monitoring and metrics
    - Ensemble methods for improved accuracy
    """

    def __init__(self,
                 embedding_model: str = "all-MiniLM-L6-v2",
                 enable_gpu: bool = True,
                 gpu_memory_limit: float = 4.0,
                 batch_size: int = 32,
                 cache_size: int = 10000):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.embedding_model_name = embedding_model

        # GPU Configuration
        self.gpu_config = gpu_config
        self.gpu_config.enable_gpu = enable_gpu
        self.gpu_config.memory_limit_gb = gpu_memory_limit
        self.gpu_config.batch_size = batch_size

        # Performance monitoring
        self.performance_metrics = {
            'model_load_times': {},
            'inference_times': {},
            'cache_hits': 0,
            'cache_misses': 0,
            'gpu_memory_usage': 0.0,
            'batch_processing_count': 0
        }

        # Initialize models
        self._init_models()

        # Enhanced scoring weights (fine-tuned for better accuracy)
        self.weights = {
            'keyword_density': 0.4,
            'emotion_intensity': 0.4,
            'novelty': 0.2
        }

        # Quality filter thresholds (optimized)
        self.quality_thresholds = {
            'min_loudness_lufs': -40.0,
            'min_asr_confidence': 0.85,
            'max_silence_gap': 2.0,
            'min_semantic_similarity': 0.3,
            'min_engagement_score': 0.2
        }

        # Enhanced Q&A detection patterns
        self.qa_patterns = self._init_qa_patterns()

        # Engagement detection patterns
        self.engagement_patterns = self._init_engagement_patterns()

        # Advanced caching system
        self.similarity_cache = {}
        self.embedding_cache = {}
        self.emotion_cache = {}
        self.max_cache_size = cache_size

        # GPU memory management
        self._setup_gpu_memory_management()

        self.logger.info(f"GPU-Accelerated HighlightsScorer initialized successfully")
        self.logger.info(f"Device: {self.gpu_config.get_device()}, GPU Enabled: {self.gpu_config.enable_gpu}")
        self.logger.info(f"Batch Size: {self.gpu_config.batch_size}, Memory Limit: {self.gpu_config.memory_limit_gb}GB")

    def _setup_gpu_memory_management(self):
        """Setup GPU memory management and monitoring"""
        if not self.gpu_config.is_gpu_available():
            return

        try:
            if TORCH_AVAILABLE:
                import torch
                if torch.cuda.is_available():
                    # Set memory fraction to prevent OOM
                    torch.cuda.set_per_process_memory_fraction(
                        self.gpu_config.memory_limit_gb / torch.cuda.get_device_properties(0).total_memory * 1e9
                    )
                    # Enable memory efficient attention if available
                    if hasattr(torch.backends.cuda, 'enable_flash_sdp'):
                        torch.backends.cuda.enable_flash_sdp(True)

                    self.logger.info(f"GPU memory management configured: {self.gpu_config.memory_limit_gb}GB limit")
        except Exception as e:
            self.logger.warning(f"Failed to setup GPU memory management: {e}")

    def _init_models(self):
        """Initialize ML models with lazy loading and GPU optimization"""
        # Set models to None - they will be loaded when first needed
        self.keybert_model = None
        self.sentiment_model = None
        self.embedding_model = None
        self.emotion_model = None
        self.ensemble_models = {}  # For ensemble emotion classification

        # Track initialization status
        self._keybert_initialized = False
        self._sentiment_initialized = False
        self._embedding_initialized = False
        self._emotion_initialized = False
        self._ensemble_initialized = False

        self.logger.info("GPU-Accelerated HighlightsScorer models initialized with lazy loading")

    def _manage_cache_size(self, cache_dict: Dict[str, Any]):
        """Manage cache size to prevent memory overflow"""
        if len(cache_dict) > self.max_cache_size:
            # Remove oldest 20% of entries
            items_to_remove = len(cache_dict) - int(self.max_cache_size * 0.8)
            keys_to_remove = list(cache_dict.keys())[:items_to_remove]
            for key in keys_to_remove:
                del cache_dict[key]

            # Force garbage collection
            if TORCH_AVAILABLE:
                import gc
                gc.collect()
                if self.gpu_config.is_gpu_available():
                    import torch
                    torch.cuda.empty_cache()

    def _get_performance_timer(self, operation_name: str):
        """Context manager for timing operations"""
        class PerformanceTimer:
            def __init__(self, scorer, name):
                self.scorer = scorer
                self.name = name
                self.start_time = 0.0

            def __enter__(self):
                self.start_time = time.time()
                return self

            def __exit__(self, exc_type, exc_val, exc_tb):
                duration = time.time() - self.start_time
                if self.name not in self.scorer.performance_metrics['inference_times']:
                    self.scorer.performance_metrics['inference_times'][self.name] = []
                self.scorer.performance_metrics['inference_times'][self.name].append(duration)

        return PerformanceTimer(self, operation_name)

    def _init_qa_patterns(self) -> Dict[str, List[str]]:
        """Initialize enhanced Q&A detection patterns"""
        return {
            'question_starters': [
                'what', 'how', 'why', 'when', 'where', 'who', 'which', 'whose',
                'can you', 'could you', 'would you', 'will you', 'should you',
                'do you', 'did you', 'have you', 'are you', 'is it', 'was it',
                'does', 'did', 'can', 'could', 'would', 'will', 'should',
                'is there', 'are there', 'was there', 'were there'
            ],
            'indirect_patterns': [
                'tell me about', 'explain', 'describe', 'elaborate on',
                'what do you think', 'what are your thoughts', 'how do you feel',
                'what would you say', 'what would happen if', 'imagine if',
                'help me understand', 'walk me through', 'break down'
            ],
            'confirmation_patterns': [
                'right?', 'correct?', 'true?', 'agree?', 'isn\'t it?', 'don\'t you think?'
            ],
            'answer_starters': [
                'yes', 'no', 'well', 'so', 'actually', 'i think', 'i believe',
                'absolutely', 'definitely', 'certainly', 'of course', 'sure',
                'that\'s', 'it\'s', 'you know', 'basically', 'essentially',
                'the answer is', 'what happens is', 'the thing is'
            ],
            'answer_patterns': [
                'because', 'since', 'the reason', 'what happens', 'the answer',
                'you see', 'the thing is', 'it depends', 'in my view',
                'from my experience', 'generally speaking', 'typically'
            ]
        }

    def _init_engagement_patterns(self) -> Dict[str, List[str]]:
        """Initialize engagement detection patterns"""
        return {
            'excitement_markers': [
                'amazing', 'incredible', 'fantastic', 'awesome', 'brilliant',
                'wow', 'unbelievable', 'extraordinary', 'remarkable', 'stunning'
            ],
            'emphasis_words': [
                'really', 'very', 'extremely', 'absolutely', 'completely',
                'totally', 'definitely', 'certainly', 'obviously', 'clearly'
            ],
            'surprise_markers': [
                'surprising', 'unexpected', 'shocking', 'believe it or not',
                'turns out', 'interestingly', 'remarkably', 'strangely'
            ],
            'storytelling_markers': [
                'once', 'then', 'suddenly', 'meanwhile', 'afterwards',
                'eventually', 'finally', 'in the end', 'it turned out'
            ]
        }

    def _get_keybert_model(self):
        """Lazy load KeyBERT model with GPU optimization"""
        if not self._keybert_initialized:
            self._keybert_initialized = True
            if KEYBERT_AVAILABLE:
                try:
                    with self._get_performance_timer("keybert_load"):
                        from keybert import KeyBERT

                        # Configure device for KeyBERT
                        device = self.gpu_config.get_device()

                        # Initialize with GPU support if available
                        if self.gpu_config.is_gpu_available():
                            self.keybert_model = KeyBERT(model=self.embedding_model_name)
                            # Move to GPU if possible
                            if hasattr(self.keybert_model.model, 'to'):
                                self.keybert_model.model = self.keybert_model.model.to(device)
                        else:
                            self.keybert_model = KeyBERT(model=self.embedding_model_name)

                        self.logger.info(f"KeyBERT model initialized successfully on {device}")

                except Exception as e:
                    self.logger.warning(f"Failed to initialize KeyBERT: {e}")
                    # Fallback to CPU
                    try:
                        from keybert import KeyBERT
                        self.keybert_model = KeyBERT(model=self.embedding_model_name)
                        self.logger.info("KeyBERT model initialized on CPU (fallback)")
                    except Exception as e2:
                        self.logger.error(f"Failed to initialize KeyBERT on CPU: {e2}")
        return self.keybert_model

    def _get_sentiment_model(self):
        """Lazy load sentiment analysis model with GPU optimization"""
        if not self._sentiment_initialized:
            self._sentiment_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    with self._get_performance_timer("sentiment_load"):
                        from transformers.pipelines import pipeline

                        # Configure device
                        device = 0 if self.gpu_config.is_gpu_available() else -1

                        self.sentiment_model = pipeline(
                            "sentiment-analysis",
                            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                            return_all_scores=True,
                            device=device
                        )

                        device_name = "GPU" if device >= 0 else "CPU"
                        self.logger.info(f"Sentiment analysis model initialized successfully on {device_name}")

                except Exception as e:
                    self.logger.warning(f"Failed to initialize sentiment model on GPU: {e}")
                    # Fallback to CPU
                    try:
                        from transformers.pipelines import pipeline
                        self.sentiment_model = pipeline(
                            "sentiment-analysis",
                            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                            return_all_scores=True,
                            device=-1
                        )
                        self.logger.info("Sentiment analysis model initialized on CPU (fallback)")
                    except Exception as e2:
                        self.logger.error(f"Failed to initialize sentiment model on CPU: {e2}")
        return self.sentiment_model

    def _get_embedding_model(self):
        """Lazy load sentence transformer model with GPU optimization"""
        if not self._embedding_initialized:
            self._embedding_initialized = True
            if SENTENCE_TRANSFORMERS_AVAILABLE:
                try:
                    with self._get_performance_timer("embedding_load"):
                        from sentence_transformers import SentenceTransformer

                        # Initialize model
                        self.embedding_model = SentenceTransformer(self.embedding_model_name)

                        # Move to GPU if available
                        if self.gpu_config.is_gpu_available():
                            device = self.gpu_config.get_device()
                            self.embedding_model = self.embedding_model.to(device)
                            self.logger.info(f"Sentence transformer model initialized successfully on {device}")
                        else:
                            self.logger.info("Sentence transformer model initialized successfully on CPU")

                except Exception as e:
                    self.logger.warning(f"Failed to initialize embedding model on GPU: {e}")
                    # Fallback to CPU
                    try:
                        from sentence_transformers import SentenceTransformer
                        self.embedding_model = SentenceTransformer(self.embedding_model_name)
                        self.embedding_model = self.embedding_model.to('cpu')
                        self.logger.info("Sentence transformer model initialized on CPU (fallback)")
                    except Exception as e2:
                        self.logger.error(f"Failed to initialize embedding model on CPU: {e2}")
        return self.embedding_model

    def _get_emotion_model(self):
        """Lazy load emotion classification model with ensemble methods"""
        if not self._emotion_initialized:
            self._emotion_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    with self._get_performance_timer("emotion_load"):
                        from transformers.pipelines import pipeline

                        # Configure device
                        device = 0 if self.gpu_config.is_gpu_available() else -1

                        # Primary emotion model
                        self.emotion_model = pipeline(
                            "text-classification",
                            model="j-hartmann/emotion-english-distilroberta-base",
                            return_all_scores=True,
                            device=device
                        )

                        device_name = "GPU" if device >= 0 else "CPU"
                        self.logger.info(f"Emotion classification model initialized successfully on {device_name}")

                except Exception as e:
                    self.logger.warning(f"Failed to initialize emotion model on GPU: {e}")
                    # Fallback to basic sentiment if emotion model fails
                    try:
                        from transformers.pipelines import pipeline
                        device = 0 if self.gpu_config.is_gpu_available() else -1
                        self.emotion_model = pipeline(
                            "sentiment-analysis",
                            model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                            return_all_scores=True,
                            device=device
                        )
                        device_name = "GPU" if device >= 0 else "CPU"
                        self.logger.info(f"Fallback sentiment model initialized successfully on {device_name}")
                    except Exception as e2:
                        # Final fallback to CPU
                        try:
                            from transformers.pipelines import pipeline
                            self.emotion_model = pipeline(
                                "sentiment-analysis",
                                model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                                return_all_scores=True,
                                device=-1
                            )
                            self.logger.info("Fallback sentiment model initialized on CPU")
                        except Exception as e3:
                            self.logger.error(f"Failed to initialize any emotion/sentiment model: {e3}")
        return self.emotion_model

    def _get_ensemble_models(self):
        """Initialize ensemble of emotion models for improved accuracy"""
        if not self._ensemble_initialized:
            self._ensemble_initialized = True
            if TRANSFORMERS_AVAILABLE:
                try:
                    with self._get_performance_timer("ensemble_load"):
                        from transformers.pipelines import pipeline

                        device = 0 if self.gpu_config.is_gpu_available() else -1

                        # Initialize multiple models for ensemble
                        models_config = [
                            ("emotion", "j-hartmann/emotion-english-distilroberta-base"),
                            ("sentiment", "cardiffnlp/twitter-roberta-base-sentiment-latest"),
                            ("emotion_alt", "SamLowe/roberta-base-go_emotions")
                        ]

                        for model_name, model_path in models_config:
                            try:
                                model = pipeline(
                                    "text-classification",
                                    model=model_path,
                                    return_all_scores=True,
                                    device=device
                                )
                                self.ensemble_models[model_name] = model
                                self.logger.info(f"Ensemble model {model_name} loaded successfully")
                            except Exception as e:
                                self.logger.warning(f"Failed to load ensemble model {model_name}: {e}")

                except Exception as e:
                    self.logger.warning(f"Failed to initialize ensemble models: {e}")

        return self.ensemble_models

    def detect_qa_patterns(self, segments: List[Dict[str, Any]]) -> Dict[int, float]:
        """
        Detect Q&A patterns in transcript segments with enhanced semantic analysis

        Args:
            segments: List of transcript segments with text and speaker info

        Returns:
            Dictionary mapping segment indices to Q&A scores (0-1)
        """
        qa_scores = {}

        for i, segment in enumerate(segments):
            text = segment.get('text', '').strip()
            speaker = segment.get('speaker', 'unknown')

            # Enhanced question detection patterns
            is_question = self._is_question_segment(text)

            qa_score = 0.0

            if is_question:
                # Look for answer in next 1-3 segments
                answer_score = self._find_answer_for_question(segments, i, speaker)
                qa_score = answer_score

                # Enhance with semantic analysis for high-potential Q&A pairs
                if answer_score > 0.3:
                    answer_text = self._get_answer_text_for_question(segments, i, speaker)
                    if answer_text:
                        semantic_score = self._calculate_qa_semantic_relevance(text, answer_text)
                        # Combine rule-based and semantic scores
                        qa_score = 0.6 * answer_score + 0.4 * semantic_score

                if qa_score > 0.0:
                    self.logger.debug(f"Q&A pattern detected at segment {i}: {text[:50]}... (score: {qa_score:.2f})")

            qa_scores[i] = qa_score

        return qa_scores

    def _calculate_qa_semantic_relevance(self, question_text: str, answer_text: str) -> float:
        """
        Calculate semantic relevance between question and answer using GPU-optimized embeddings

        Args:
            question_text: The question text
            answer_text: The answer text

        Returns:
            Semantic relevance score (0-1)
        """
        embedding_model = self._get_embedding_model()
        if not embedding_model or not question_text.strip() or not answer_text.strip():
            return 0.5  # Default score when embeddings unavailable

        try:
            with self._get_performance_timer("qa_semantic_relevance"):
                # Create cache key for performance
                cache_key = f"qa_sem_{hash(question_text + answer_text)}"
                if cache_key in self.similarity_cache:
                    self.performance_metrics['cache_hits'] += 1
                    return self.similarity_cache[cache_key]

                self.performance_metrics['cache_misses'] += 1

                # Generate embeddings with batch processing for efficiency
                texts = [question_text, answer_text]
                embeddings = embedding_model.encode(
                    texts,
                    batch_size=min(len(texts), self.gpu_config.batch_size),
                    show_progress_bar=False,
                    convert_to_numpy=True
                )

                question_embedding = embeddings[0:1]
                answer_embedding = embeddings[1:2]

                if SKLEARN_AVAILABLE:
                    from sklearn.metrics.pairwise import cosine_similarity
                    similarity = cosine_similarity(question_embedding, answer_embedding)[0][0]

                    # Enhanced relevance calculation with multiple factors
                    base_similarity = max(0.0, min(1.0, similarity))

                    # Add semantic coherence bonus
                    coherence_bonus = self._calculate_semantic_coherence(question_text, answer_text)

                    # Combine scores
                    relevance_score = 0.8 * base_similarity + 0.2 * coherence_bonus
                    relevance_score = max(0.0, min(1.0, relevance_score))

                    # Cache the result with size management
                    self.similarity_cache[cache_key] = relevance_score
                    self._manage_cache_size(self.similarity_cache)

                    return relevance_score
                else:
                    return 0.5

        except Exception as e:
            self.logger.warning(f"Error calculating semantic relevance: {e}")
            return 0.5

    def _calculate_semantic_coherence(self, question_text: str, answer_text: str) -> float:
        """
        Calculate semantic coherence between question and answer using linguistic patterns

        Args:
            question_text: The question text
            answer_text: The answer text

        Returns:
            Coherence score (0-1)
        """
        try:
            # Extract key terms from question
            question_words = set(question_text.lower().split())
            answer_words = set(answer_text.lower().split())

            # Remove common stop words
            stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
            question_words -= stop_words
            answer_words -= stop_words

            if not question_words or not answer_words:
                return 0.5

            # Calculate word overlap
            overlap = len(question_words.intersection(answer_words))
            total_unique = len(question_words.union(answer_words))

            if total_unique == 0:
                return 0.5

            word_overlap_score = overlap / total_unique

            # Check for answer patterns that respond to question types
            coherence_patterns = self._check_qa_coherence_patterns(question_text, answer_text)

            # Combine scores
            coherence_score = 0.6 * word_overlap_score + 0.4 * coherence_patterns
            return max(0.0, min(1.0, coherence_score))

        except Exception as e:
            self.logger.warning(f"Error calculating semantic coherence: {e}")
            return 0.5

    def _check_qa_coherence_patterns(self, question_text: str, answer_text: str) -> float:
        """
        Check for coherence patterns between question and answer types

        Args:
            question_text: The question text
            answer_text: The answer text

        Returns:
            Pattern coherence score (0-1)
        """
        question_lower = question_text.lower()
        answer_lower = answer_text.lower()

        coherence_score = 0.0

        # What questions should have descriptive answers
        if question_lower.startswith('what'):
            if any(word in answer_lower for word in ['is', 'are', 'means', 'refers', 'involves']):
                coherence_score += 0.3

        # How questions should have process/method answers
        elif question_lower.startswith('how'):
            if any(word in answer_lower for word in ['by', 'through', 'using', 'first', 'then', 'step']):
                coherence_score += 0.3

        # Why questions should have causal answers
        elif question_lower.startswith('why'):
            if any(word in answer_lower for word in ['because', 'since', 'due to', 'reason', 'cause']):
                coherence_score += 0.3

        # When questions should have temporal answers
        elif question_lower.startswith('when'):
            if any(word in answer_lower for word in ['time', 'during', 'after', 'before', 'while', 'year', 'day']):
                coherence_score += 0.3

        # Where questions should have location answers
        elif question_lower.startswith('where'):
            if any(word in answer_lower for word in ['in', 'at', 'on', 'location', 'place', 'here', 'there']):
                coherence_score += 0.3

        # Check for direct answer patterns
        if any(starter in answer_lower for starter in self.qa_patterns['answer_starters']):
            coherence_score += 0.2

        return min(1.0, coherence_score)

    def _is_question_segment(self, text: str) -> bool:
        """
        Enhanced question detection using multiple patterns

        Args:
            text: Text to analyze

        Returns:
            True if text appears to be a question
        """
        text_lower = text.lower().strip()

        # Direct question markers
        if text.endswith('?'):
            return True

        # Use enhanced patterns from initialization
        if any(text_lower.startswith(starter) for starter in self.qa_patterns['question_starters']):
            return True

        if any(pattern in text_lower for pattern in self.qa_patterns['indirect_patterns']):
            return True

        # Rising intonation indicators (common in speech-to-text)
        if any(phrase in text_lower for phrase in self.qa_patterns['confirmation_patterns']):
            return True

        return False

    def _find_answer_for_question(self, segments: List[Dict[str, Any]],
                                 question_idx: int, question_speaker: str) -> float:
        """
        Find and score potential answers following a question (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Score indicating quality of Q&A pattern (0-1)
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        best_score = 0.0

        for i in range(1, max_lookahead + 1):
            answer_idx = question_idx + i
            if answer_idx >= len(segments):
                break

            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip if very short response
            if len(answer_text) < 10:
                continue

            # Skip if same speaker (only when we have speaker info)
            if (question_speaker != 'unknown' and answer_speaker != 'unknown' and
                answer_speaker == question_speaker):
                continue

            # Score based on answer quality indicators
            score = 0.0

            # Different speaker responding = good (when we have speaker info)
            if (answer_speaker != 'unknown' and question_speaker != 'unknown' and
                answer_speaker != question_speaker):
                score += 0.4
            elif answer_speaker == 'unknown' or question_speaker == 'unknown':
                # Base score when no speaker info available
                score += 0.2

            # Answer length indicates substantive response (more important without speaker info)
            if len(answer_text) > 50:
                score += 0.4
            elif len(answer_text) > 30:
                score += 0.3
            elif len(answer_text) > 15:
                score += 0.2

            # Answer starts with typical response patterns (use enhanced patterns)
            answer_lower = answer_text.lower()
            if any(answer_lower.startswith(starter) for starter in self.qa_patterns['answer_starters']):
                score += 0.2

            # Look for answer-like content patterns (use enhanced patterns)
            if any(pattern in answer_lower for pattern in self.qa_patterns['answer_patterns']):
                score += 0.15

            # Bonus for immediate response (next segment)
            if i == 1:
                score += 0.1

            # Penalty for questions as answers
            if answer_text.strip().endswith('?'):
                score *= 0.5

            best_score = max(best_score, min(1.0, score))

        return best_score

    def _get_answer_text_for_question(self, segments: List[Dict[str, Any]],
                                     question_idx: int, question_speaker: str) -> str:
        """
        Get the answer text for OpenAI enhancement (works without speaker diarization)

        Args:
            segments: All transcript segments
            question_idx: Index of the question segment
            question_speaker: Speaker who asked the question (may be 'unknown')

        Returns:
            Combined answer text from responding segments
        """
        max_lookahead = min(3, len(segments) - question_idx - 1)
        answer_parts = []

        for i in range(1, max_lookahead + 1):
            answer_idx = question_idx + i
            if answer_idx >= len(segments):
                break

            answer_segment = segments[answer_idx]
            answer_speaker = answer_segment.get('speaker', 'unknown')
            answer_text = answer_segment.get('text', '').strip()

            # Skip very short segments
            if len(answer_text) < 10:
                continue

            # Include if different speaker or when no speaker info available
            if question_speaker == 'unknown' or answer_speaker == 'unknown':
                # No speaker info - include if it looks like an answer
                if not answer_text.strip().endswith('?'):  # Not another question
                    answer_parts.append(answer_text)
                else:
                    break  # Stop at next question
            elif answer_speaker != question_speaker:
                # Different speaker - include
                answer_parts.append(answer_text)
            elif len(answer_parts) > 0 and answer_speaker == segments[question_idx + 1].get('speaker'):
                # Continuation of answer from same speaker
                answer_parts.append(answer_text)
            else:
                break  # Stop at speaker change back to questioner

        return ' '.join(answer_parts)

    def calculate_keyword_density(self, text: str, keywords: List[str]) -> float:
        """
        Calculate keyword density using KeyBERT embeddings with OpenAI enhancement

        Args:
            text: Text to analyze
            keywords: List of target keywords

        Returns:
            Keyword density score (0-1)
        """
        if not keywords or not text.strip():
            return 0.0

        # Start with KeyBERT-based scoring
        keybert_score = 0.0
        keybert_model = self._get_keybert_model()
        if keybert_model:
            try:
                # Extract keywords from text using KeyBERT
                extracted_keywords = keybert_model.extract_keywords(
                    text,
                    keyphrase_ngram_range=(1, 2),
                    stop_words='english'
                )[:10]  # Limit to top 10 keywords

                if extracted_keywords:
                    # Calculate similarity between extracted keywords and target keywords
                    extracted_terms = [kw[0] if isinstance(kw, tuple) else str(kw) for kw in extracted_keywords]
                    extracted_scores = [kw[1] if isinstance(kw, tuple) else 1.0 for kw in extracted_keywords]

                    # Simple keyword matching with weights
                    total_score = 0.0
                    for keyword in keywords:
                        keyword_lower = keyword.lower()
                        for i, term in enumerate(extracted_terms):
                            if keyword_lower in term.lower() or term.lower() in keyword_lower:
                                total_score += extracted_scores[i]

                    # Normalize by number of keywords
                    keybert_score = min(total_score / len(keywords), 1.0)

            except Exception as e:
                self.logger.warning(f"Error calculating KeyBERT keyword density: {e}")

        # For cost-effectiveness, only use OpenAI for high-potential spans
        # This will be called later in a batch process for top spans
        return min(1.0, max(0.0, keybert_score))

    def calculate_emotion_intensity(self, text: str, audio_features: Optional[Dict] = None) -> float:
        """
        Calculate emotion intensity using GPU-accelerated ensemble methods and engagement detection

        Args:
            text: Text to analyze
            audio_features: Optional audio features (loudness, prosody, etc.)

        Returns:
            Emotion intensity score (0-1)
        """
        if not text.strip():
            return 0.0

        try:
            with self._get_performance_timer("emotion_intensity"):
                # Check cache first
                cache_key = f"emotion_{hash(text)}"
                if cache_key in self.emotion_cache:
                    self.performance_metrics['cache_hits'] += 1
                    cached_result = self.emotion_cache[cache_key]

                    # Add audio score if available
                    if audio_features:
                        audio_score = self._calculate_audio_emotion_score(audio_features)
                        return 0.8 * cached_result + 0.2 * audio_score
                    return cached_result

                self.performance_metrics['cache_misses'] += 1

                # Calculate ensemble emotion score
                ensemble_score = self._calculate_ensemble_emotion_score(text)

                # Enhanced engagement detection using patterns
                engagement_score = self._calculate_engagement_score(text)

                # Linguistic complexity score
                complexity_score = self._calculate_linguistic_complexity(text)

                # Combine text-based scores
                text_emotion_score = (
                    0.5 * ensemble_score +
                    0.3 * engagement_score +
                    0.2 * complexity_score
                )

                # Audio-based emotion features (if available)
                if audio_features:
                    audio_score = self._calculate_audio_emotion_score(audio_features)
                    emotion_intensity = 0.7 * text_emotion_score + 0.3 * audio_score
                else:
                    emotion_intensity = text_emotion_score

                # Cache the text-based result
                self.emotion_cache[cache_key] = text_emotion_score
                self._manage_cache_size(self.emotion_cache)

                return min(1.0, max(0.0, emotion_intensity))

        except Exception as e:
            self.logger.warning(f"Error calculating emotion intensity: {e}")
            return 0.5

    def _calculate_ensemble_emotion_score(self, text: str) -> float:
        """
        Calculate emotion score using ensemble of models for improved accuracy

        Args:
            text: Text to analyze

        Returns:
            Ensemble emotion score (0-1)
        """
        try:
            scores = []

            # Primary emotion model
            emotion_model = self._get_emotion_model()
            if emotion_model:
                try:
                    results = emotion_model(text)
                    if results and isinstance(results, list) and len(results) > 0:
                        if isinstance(results[0], list):
                            max_score = max(result['score'] for result in results[0])
                            scores.append(max_score)
                        else:
                            scores.append(results[0].get('score', 0.0))
                except Exception as e:
                    self.logger.warning(f"Error in primary emotion model: {e}")

            # Ensemble models (if available)
            ensemble_models = self._get_ensemble_models()
            for model_name, model in ensemble_models.items():
                try:
                    results = model(text)
                    if results and isinstance(results, list) and len(results) > 0:
                        if isinstance(results[0], list):
                            max_score = max(result['score'] for result in results[0])
                            scores.append(max_score)
                        else:
                            scores.append(results[0].get('score', 0.0))
                except Exception as e:
                    self.logger.warning(f"Error in ensemble model {model_name}: {e}")

            if scores:
                # Use weighted average with higher weight for primary model
                if len(scores) == 1:
                    return scores[0]
                else:
                    # Primary model gets 50% weight, others split remaining 50%
                    weights = [0.5] + [0.5 / (len(scores) - 1)] * (len(scores) - 1)
                    ensemble_score = sum(score * weight for score, weight in zip(scores, weights))
                    return min(1.0, max(0.0, ensemble_score))
            else:
                return 0.5

        except Exception as e:
            self.logger.warning(f"Error in ensemble emotion scoring: {e}")
            return 0.5

    def _calculate_audio_emotion_score(self, audio_features: Dict) -> float:
        """
        Calculate emotion score from audio features

        Args:
            audio_features: Audio features dictionary

        Returns:
            Audio emotion score (0-1)
        """
        try:
            audio_score = 0.0

            # Use loudness as a proxy for emotional intensity
            loudness = audio_features.get('loudness', -30.0)
            # Normalize loudness to 0-1 scale (assuming range -60 to 0 dB)
            loudness_score = max(0.0, min(1.0, (loudness + 60) / 60))
            audio_score += 0.4 * loudness_score

            # Use pitch variation if available
            pitch_variation = audio_features.get('pitch_variation', 0.5)
            audio_score += 0.3 * min(1.0, max(0.0, pitch_variation))

            # Use speaking rate if available
            speaking_rate = audio_features.get('speaking_rate', 1.0)
            # Higher speaking rate can indicate excitement
            rate_score = min(1.0, max(0.0, (speaking_rate - 0.5) / 1.5))
            audio_score += 0.3 * rate_score

            return min(1.0, max(0.0, audio_score))

        except Exception as e:
            self.logger.warning(f"Error processing audio features: {e}")
            return 0.5

    def _calculate_linguistic_complexity(self, text: str) -> float:
        """
        Calculate linguistic complexity score for content depth assessment

        Args:
            text: Text to analyze

        Returns:
            Complexity score (0-1)
        """
        try:
            if not text.strip():
                return 0.0

            words = text.split()
            if not words:
                return 0.0

            complexity_score = 0.0

            # Average word length (longer words often indicate complexity)
            avg_word_length = sum(len(word) for word in words) / len(words)
            length_score = min(1.0, (avg_word_length - 3) / 7)  # Normalize around 3-10 chars
            complexity_score += 0.3 * max(0.0, length_score)

            # Sentence structure complexity
            sentences = text.split('.')
            if len(sentences) > 1:
                avg_sentence_length = len(words) / len(sentences)
                sentence_score = min(1.0, (avg_sentence_length - 5) / 20)  # Normalize around 5-25 words
                complexity_score += 0.3 * max(0.0, sentence_score)

            # Vocabulary diversity
            unique_words = len(set(word.lower() for word in words))
            diversity_score = unique_words / len(words) if words else 0
            complexity_score += 0.4 * diversity_score

            return min(1.0, max(0.0, complexity_score))

        except Exception as e:
            self.logger.warning(f"Error calculating linguistic complexity: {e}")
            return 0.5

    def _calculate_engagement_score(self, text: str) -> float:
        """
        Calculate engagement score based on linguistic patterns

        Args:
            text: Text to analyze

        Returns:
            Engagement score (0-1)
        """
        if not text.strip():
            return 0.0

        text_lower = text.lower()
        engagement_score = 0.0

        # Check for excitement markers
        excitement_count = sum(1 for marker in self.engagement_patterns['excitement_markers']
                             if marker in text_lower)
        engagement_score += min(0.3, excitement_count * 0.1)

        # Check for emphasis words
        emphasis_count = sum(1 for word in self.engagement_patterns['emphasis_words']
                           if word in text_lower)
        engagement_score += min(0.2, emphasis_count * 0.05)

        # Check for surprise markers
        surprise_count = sum(1 for marker in self.engagement_patterns['surprise_markers']
                           if marker in text_lower)
        engagement_score += min(0.2, surprise_count * 0.1)

        # Check for storytelling markers
        story_count = sum(1 for marker in self.engagement_patterns['storytelling_markers']
                        if marker in text_lower)
        engagement_score += min(0.15, story_count * 0.05)

        # Check for exclamation marks and caps
        if '!' in text:
            engagement_score += 0.1
        if any(word.isupper() and len(word) > 2 for word in text.split()):
            engagement_score += 0.05

        return min(1.0, engagement_score)

    def calculate_novelty_score(self, current_text: str, context_texts: List[str]) -> float:
        """
        Calculate novelty score based on similarity to recent context

        Args:
            current_text: Current text segment
            context_texts: List of previous text segments (60s window)

        Returns:
            Novelty score (0-1, higher = more novel)
        """
        embedding_model = self._get_embedding_model()
        if not embedding_model or not current_text.strip() or not context_texts:
            return 0.5  # Default novelty score

        try:
            # Limit context size to prevent memory issues (max 10 most recent segments)
            limited_context = context_texts[-10:] if len(context_texts) > 10 else context_texts

            if not limited_context:
                return 0.5

            # Generate embeddings
            current_embedding = embedding_model.encode([current_text])
            context_embeddings = embedding_model.encode(limited_context)

            if SKLEARN_AVAILABLE:
                # Import sklearn here for lazy loading
                from sklearn.metrics.pairwise import cosine_similarity
                # Calculate mean similarity to context
                similarities = cosine_similarity(current_embedding, context_embeddings)[0]
                mean_similarity = np.mean(similarities)

                # Novelty is inverse of similarity
                novelty_score = 1.0 - mean_similarity
                return max(0.0, min(1.0, novelty_score))
            else:
                return 0.5

        except Exception as e:
            self.logger.warning(f"Error calculating novelty score: {e}")
            return 0.5

    def apply_quality_filters(self, segment: Dict[str, Any], audio_features: Optional[Dict] = None) -> bool:
        """
        Apply quality filters to determine if segment should be included

        Args:
            segment: Transcript segment with metadata
            audio_features: Optional audio features

        Returns:
            True if segment passes quality filters, False otherwise
        """
        # Check ASR confidence
        confidence = segment.get('confidence', 1.0)
        if confidence < self.quality_thresholds['min_asr_confidence']:
            self.logger.debug(f"Segment filtered: low ASR confidence ({confidence:.2f})")
            return False

        # Check audio loudness (if available)
        if audio_features:
            loudness = audio_features.get('loudness', 0.0)
            if loudness < self.quality_thresholds['min_loudness_lufs']:
                self.logger.debug(f"Segment filtered: low audio loudness ({loudness:.1f} LUFS)")
                return False

        # Check for excessive silence (placeholder - would need audio analysis)
        # This would require actual audio processing to detect silence gaps

        return True

    def calculate_composite_score(self, keyword_density: float,
                                emotion_intensity: float, novelty: float) -> float:
        """
        Calculate composite score using weighted combination

        Args:
            keyword_density: Keyword density score (0-1)
            emotion_intensity: Emotion intensity score (0-1)
            novelty: Novelty score (0-1)

        Returns:
            Composite score (0-1)
        """
        composite_score = (
            self.weights['keyword_density'] * keyword_density +
            self.weights['emotion_intensity'] * emotion_intensity +
            self.weights['novelty'] * novelty
        )

        return min(1.0, max(0.0, composite_score))

    def batch_process_segments(self, segments: List[Dict[str, Any]],
                              keywords: Optional[List[str]] = None) -> List[Dict[str, float]]:
        """
        Process multiple segments in batches for optimal GPU utilization

        Args:
            segments: List of transcript segments
            keywords: Optional keywords for density calculation

        Returns:
            List of scoring results for each segment
        """
        if not segments:
            return []

        try:
            with self._get_performance_timer("batch_processing"):
                self.performance_metrics['batch_processing_count'] += 1

                results = []
                batch_size = self.gpu_config.batch_size

                # Process segments in batches
                for i in range(0, len(segments), batch_size):
                    batch = segments[i:i + batch_size]
                    batch_results = self._process_segment_batch(batch, keywords)
                    results.extend(batch_results)

                    # GPU memory management
                    if self.gpu_config.is_gpu_available() and i % (batch_size * 4) == 0:
                        self._cleanup_gpu_memory()

                return results

        except Exception as e:
            self.logger.error(f"Error in batch processing: {e}")
            # Fallback to individual processing
            return [self._process_single_segment(seg, keywords) for seg in segments]

    def _process_segment_batch(self, batch: List[Dict[str, Any]],
                              keywords: Optional[List[str]] = None) -> List[Dict[str, float]]:
        """
        Process a batch of segments efficiently

        Args:
            batch: Batch of segments to process
            keywords: Optional keywords for density calculation

        Returns:
            List of scoring results
        """
        results = []

        try:
            # Extract texts for batch processing
            texts = [seg.get('text', '') for seg in batch]

            # Batch emotion processing if possible
            emotion_scores = self._batch_emotion_analysis(texts)

            # Batch embedding processing for novelty
            embeddings = self._batch_embedding_generation(texts)

            # Process each segment with batch results
            for i, segment in enumerate(batch):
                text = segment.get('text', '')

                # Q&A detection (requires individual processing due to context)
                qa_score = self._calculate_qa_score_for_segment(segment, batch)

                # Keyword density
                keyword_score = self.calculate_keyword_density(text, keywords) if keywords else 0.0

                # Use batch emotion result
                emotion_score = emotion_scores[i] if i < len(emotion_scores) else 0.0

                # Novelty (use batch embeddings)
                novelty_score = self._calculate_novelty_from_embeddings(
                    embeddings[i] if i < len(embeddings) else None,
                    embeddings
                )

                # Composite score
                composite_score = self.calculate_composite_score(
                    qa_score, keyword_score, emotion_score, novelty_score
                )

                results.append({
                    'qa_score': qa_score,
                    'keyword_density': keyword_score,
                    'emotion_intensity': emotion_score,
                    'novelty': novelty_score,
                    'composite_score': composite_score
                })

        except Exception as e:
            self.logger.warning(f"Error in batch processing: {e}")
            # Fallback to individual processing
            for segment in batch:
                results.append(self._process_single_segment(segment, keywords))

        return results



    def _calculate_novelty_from_embeddings(self, current_embedding: Optional[np.ndarray],
                                         all_embeddings: List[Optional[np.ndarray]]) -> float:
        """
        Calculate novelty score using pre-computed embeddings

        Args:
            current_embedding: Embedding for current text
            all_embeddings: List of all embeddings for context

        Returns:
            Novelty score (0-1)
        """
        try:
            if current_embedding is None or not all_embeddings:
                return 0.5

            # Filter out None embeddings
            valid_embeddings = [emb for emb in all_embeddings if emb is not None]
            if len(valid_embeddings) < 2:
                return 1.0  # High novelty if no context

            if SKLEARN_AVAILABLE:
                from sklearn.metrics.pairwise import cosine_similarity

                # Calculate similarity with all other embeddings
                similarities = []
                for other_embedding in valid_embeddings:
                    if not np.array_equal(current_embedding, other_embedding):
                        sim = cosine_similarity(current_embedding.reshape(1, -1), other_embedding.reshape(1, -1))[0][0]
                        similarities.append(sim)

                if similarities:
                    # Novelty is inverse of maximum similarity
                    max_similarity = max(similarities)
                    novelty_score = 1.0 - max_similarity
                    return max(0.0, min(1.0, novelty_score))
                else:
                    return 1.0
            else:
                return 0.5

        except Exception as e:
            self.logger.warning(f"Error calculating novelty from embeddings: {e}")
            return 0.5

    def _batch_emotion_analysis(self, texts: List[str]) -> List[float]:
        """
        Perform batch emotion analysis for efficiency

        Args:
            texts: List of texts to analyze

        Returns:
            List of emotion scores
        """
        try:
            emotion_model = self._get_emotion_model()
            if not emotion_model or not texts:
                return [0.5] * len(texts)

            # Filter out empty texts
            valid_texts = [(i, text) for i, text in enumerate(texts) if text.strip()]
            if not valid_texts:
                return [0.0] * len(texts)

            indices, filtered_texts = zip(*valid_texts)

            # Batch processing
            results = emotion_model(list(filtered_texts))

            # Map results back to original indices
            scores = [0.0] * len(texts)
            if results:
                for i, result in enumerate(results):
                    if i < len(indices):
                        original_idx = indices[i]
                        if isinstance(result, list) and result:
                            scores[original_idx] = max(float(r.get('score', 0.0)) for r in result if isinstance(r, dict))
                        elif isinstance(result, dict):
                            scores[original_idx] = float(result.get('score', 0.0))

            return scores

        except Exception as e:
            self.logger.warning(f"Error in batch emotion analysis: {e}")
            return [0.5] * len(texts)

    def _batch_embedding_generation(self, texts: List[str]) -> List[Union[np.ndarray, None]]:
        """
        Generate embeddings in batch for efficiency

        Args:
            texts: List of texts to embed

        Returns:
            List of embeddings (or None for failed embeddings)
        """
        try:
            embedding_model = self._get_embedding_model()
            if not embedding_model or not texts:
                return [None] * len(texts)

            # Filter out empty texts
            valid_texts = [(i, text) for i, text in enumerate(texts) if text.strip()]
            if not valid_texts:
                return [None] * len(texts)

            indices, filtered_texts = zip(*valid_texts)

            # Batch embedding generation
            embeddings = embedding_model.encode(
                list(filtered_texts),
                batch_size=min(len(filtered_texts), self.gpu_config.batch_size),
                show_progress_bar=False,
                convert_to_numpy=True
            )

            # Map embeddings back to original indices
            result_embeddings = [None] * len(texts)
            for i, embedding in enumerate(embeddings):
                original_idx = indices[i]
                result_embeddings[original_idx] = embedding

            return result_embeddings

        except Exception as e:
            self.logger.warning(f"Error in batch embedding generation: {e}")
            return [None] * len(texts)

    def _cleanup_gpu_memory(self):
        """Clean up GPU memory to prevent OOM errors"""
        try:
            if TORCH_AVAILABLE and self.gpu_config.is_gpu_available():
                import torch
                import gc

                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                    # Log memory usage
                    memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                    self.performance_metrics['gpu_memory_usage'] = memory_allocated

                    if memory_allocated > self.gpu_config.memory_limit_gb * 0.8:
                        self.logger.warning(f"High GPU memory usage: {memory_allocated:.2f}GB")

        except Exception as e:
            self.logger.warning(f"Error cleaning up GPU memory: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance metrics

        Returns:
            Dictionary of performance metrics
        """
        metrics = self.performance_metrics.copy()

        # Calculate average inference times
        avg_times = {}
        for operation, times in metrics['inference_times'].items():
            if times:
                avg_times[operation] = {
                    'avg_ms': np.mean(times) * 1000,
                    'min_ms': np.min(times) * 1000,
                    'max_ms': np.max(times) * 1000,
                    'count': len(times)
                }
        metrics['avg_inference_times'] = avg_times

        # Calculate cache efficiency
        total_requests = metrics['cache_hits'] + metrics['cache_misses']
        if total_requests > 0:
            metrics['cache_hit_rate'] = metrics['cache_hits'] / total_requests
        else:
            metrics['cache_hit_rate'] = 0.0

        # Add GPU information
        metrics['gpu_config'] = {
            'device': self.gpu_config.get_device(),
            'gpu_enabled': self.gpu_config.enable_gpu,
            'gpu_available': self.gpu_config.is_gpu_available(),
            'batch_size': self.gpu_config.batch_size,
            'memory_limit_gb': self.gpu_config.memory_limit_gb
        }

        return metrics

    def _process_single_segment(self, segment: Dict[str, Any],
                               keywords: Optional[List[str]] = None) -> Dict[str, float]:
        """
        Process a single segment (fallback method)

        Args:
            segment: Segment to process
            keywords: Optional keywords

        Returns:
            Scoring results
        """
        text = segment.get('text', '')

        qa_score = 0.0  # Would need context for proper Q&A detection
        keyword_score = self.calculate_keyword_density(text, keywords) if keywords else 0.0
        emotion_score = self.calculate_emotion_intensity(text)
        novelty_score = 0.5  # Default when no context available

        composite_score = self.calculate_composite_score(
            qa_score, keyword_score, emotion_score, novelty_score
        )

        return {
            'qa_score': qa_score,
            'keyword_density': keyword_score,
            'emotion_intensity': emotion_score,
            'novelty': novelty_score,
            'composite_score': composite_score
        }


# Create a global instance for easy access with GPU optimization
highlights_scorer = HighlightsScorer()
