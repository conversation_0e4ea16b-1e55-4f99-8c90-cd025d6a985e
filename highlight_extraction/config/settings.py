#!/usr/bin/env python3
"""
Configuration settings for highlight extraction functionality
"""

import os
from pathlib import Path

# Base directories
HIGHLIGHT_EXTRACTION_ROOT = Path(__file__).parent.parent
HIGHLIGHTS_OUTPUT_DIR = os.environ.get('HIGHLIGHTS_OUTPUT_DIR', 'output/highlights')
HIGHLIGHTS_LOGS_DIR = os.environ.get('HIGHLIGHTS_LOGS_DIR', 'logs/highlights')

# Job execution settings
MAX_CONCURRENT_JOBS = int(os.environ.get('HIGHLIGHTS_MAX_CONCURRENT_JOBS', '2'))
JOB_TIMEOUT_SECONDS = int(os.environ.get('HIGHLIGHTS_JOB_TIMEOUT', '3600'))  # 1 hour
JOB_CLEANUP_DAYS = int(os.environ.get('HIGHLIGHTS_JOB_CLEANUP_DAYS', '7'))

# Highlights extraction settings
DEFAULT_TARGET_LENGTH = int(os.environ.get('HIGHLIGHTS_TARGET_LENGTH', '75'))  # seconds
MIN_SPAN_DURATION = float(os.environ.get('HIGHLIGHTS_MIN_SPAN_DURATION', '10.0'))  # seconds
MAX_SPAN_DURATION = float(os.environ.get('HIGHLIGHTS_MAX_SPAN_DURATION', '30.0'))  # seconds
PADDING_SECONDS = float(os.environ.get('HIGHLIGHTS_PADDING_SECONDS', '1.0'))

# Scoring weights (must sum to 1.0)
SCORING_WEIGHTS = {
    'keyword_density': float(os.environ.get('HIGHLIGHTS_KEYWORD_WEIGHT', '0.4')),
    'emotion_intensity': float(os.environ.get('HIGHLIGHTS_EMOTION_WEIGHT', '0.4')),
    'novelty': float(os.environ.get('HIGHLIGHTS_NOVELTY_WEIGHT', '0.2'))
}

# Quality filters
MIN_QUALITY_SCORE = float(os.environ.get('HIGHLIGHTS_MIN_QUALITY_SCORE', '0.3'))
MIN_WORDS_PER_SPAN = int(os.environ.get('HIGHLIGHTS_MIN_WORDS_PER_SPAN', '10'))
MAX_SILENCE_RATIO = float(os.environ.get('HIGHLIGHTS_MAX_SILENCE_RATIO', '0.4'))

# Video output settings
OUTPUT_RESOLUTION = os.environ.get('HIGHLIGHTS_OUTPUT_RESOLUTION', '720x1280')  # 9:16 aspect ratio
OUTPUT_FORMAT = os.environ.get('HIGHLIGHTS_OUTPUT_FORMAT', 'mp4')
OUTPUT_CODEC = os.environ.get('HIGHLIGHTS_OUTPUT_CODEC', 'libx264')
OUTPUT_QUALITY = os.environ.get('HIGHLIGHTS_OUTPUT_QUALITY', 'medium')

# Logging settings
LOG_LEVEL = os.environ.get('HIGHLIGHTS_LOG_LEVEL', 'INFO')
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Cost optimization - disable expensive API calls by default
OPENAI_ENABLED = os.environ.get('HIGHLIGHTS_OPENAI_ENABLED', 'false').lower() == 'true'
OPENAI_MODEL = os.environ.get('HIGHLIGHTS_OPENAI_MODEL', 'gpt-3.5-turbo')
OPENAI_MAX_REQUESTS = int(os.environ.get('HIGHLIGHTS_OPENAI_MAX_REQUESTS', '5'))

# GPU settings
USE_GPU = os.environ.get('HIGHLIGHTS_USE_GPU', 'true').lower() == 'true'
GPU_MEMORY_LIMIT = os.environ.get('HIGHLIGHTS_GPU_MEMORY_LIMIT', '4GB')

# Create directories if they don't exist
os.makedirs(HIGHLIGHTS_OUTPUT_DIR, exist_ok=True)
os.makedirs(HIGHLIGHTS_LOGS_DIR, exist_ok=True)
