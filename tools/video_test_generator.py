#!/usr/bin/env python3
"""
Comprehensive Video Test Generator for Enhanced Face Positioning System

This module generates synthetic test videos with realistic face detection data
to demonstrate and validate the enhanced face positioning system capabilities.

Features:
- Synthetic video generation with simulated face movements
- Realistic face detection data mimicking MediaPipe output
- Multiple test scenarios covering all layout types
- Visual validation with overlay information
- Performance metrics and analysis
"""

import cv2
import numpy as np
import json
import os
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import time
import sys

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from reframing.models.data_classes import FaceDetection
from reframing.video.face_positioning import FacePositioningEngine, FaceLayoutType


@dataclass
class TestFace:
    """Represents a test face with movement parameters"""
    id: int
    start_time: float
    end_time: float
    start_pos: Tuple[int, int]
    end_pos: Tuple[int, int]
    size: Tuple[int, int]
    confidence: float
    movement_type: str  # 'linear', 'circular', 'stationary', 'bounce'
    color: Tuple[int, int, int]  # BGR color for visualization


@dataclass
class VideoScenario:
    """Defines a test video scenario"""
    name: str
    duration: float
    description: str
    faces: List[TestFace]
    target_features: List[str]  # Features this scenario is designed to test


class VideoTestGenerator:
    """
    Comprehensive video test generator for face positioning validation
    """
    
    def __init__(self, output_dir: str = "test_videos"):
        self.logger = logging.getLogger(__name__)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Video parameters
        self.width = 1920
        self.height = 1080
        self.fps = 30
        self.target_width = 720
        self.target_height = 1280
        
        # Face positioning engine for testing
        self.face_engine = FacePositioningEngine()
        
        # Colors for visualization
        self.colors = {
            'face_box': (0, 255, 0),      # Green for face boxes
            'region_box': (255, 0, 0),    # Blue for layout regions
            'crop_box': (0, 0, 255),      # Red for crop area
            'prediction': (255, 255, 0),  # Cyan for predictions
            'text': (255, 255, 255),      # White for text
            'background': (50, 50, 50)    # Dark gray background
        }

    def generate_all_test_videos(self) -> Dict[str, str]:
        """
        Generate all test video scenarios
        
        Returns:
            Dictionary mapping scenario names to output file paths
        """
        self.logger.info("🎬 Starting comprehensive video test generation")
        
        scenarios = self._create_test_scenarios()
        generated_videos = {}
        
        for scenario in scenarios:
            self.logger.info(f"📹 Generating scenario: {scenario.name}")
            video_path = self._generate_scenario_video(scenario)
            generated_videos[scenario.name] = video_path
            
        # Generate comparison videos
        self.logger.info("🔄 Generating comparison videos")
        self._generate_comparison_videos(generated_videos)
        
        self.logger.info("✅ All test videos generated successfully")
        return generated_videos

    def _create_test_scenarios(self) -> List[VideoScenario]:
        """Create all test scenarios"""
        scenarios = []
        
        # Scenario 1: Layout Transition Demo
        scenarios.append(VideoScenario(
            name="layout_transitions",
            duration=30.0,
            description="Demonstrates smooth transitions between all layout types",
            faces=[
                # Single face (0-7s)
                TestFace(1, 0.0, 7.0, (960, 540), (960, 540), (200, 200), 0.92, 'stationary', (255, 100, 100)),
                
                # Two faces (7-15s)
                TestFace(2, 7.0, 15.0, (1400, 540), (700, 540), (180, 180), 0.89, 'linear', (100, 255, 100)),
                
                # Three faces (15-23s)
                TestFace(3, 15.0, 23.0, (1400, 300), (1200, 300), (160, 160), 0.87, 'linear', (100, 100, 255)),
                
                # Four faces (23-30s)
                TestFace(4, 23.0, 30.0, (1400, 800), (1200, 800), (150, 150), 0.85, 'linear', (255, 255, 100))
            ],
            target_features=["layout_transitions", "smooth_transitions", "face_assignments"]
        ))
        
        # Scenario 2: Predictive Tracking Demo
        scenarios.append(VideoScenario(
            name="predictive_tracking",
            duration=20.0,
            description="Single face moving to demonstrate 2-3 second lookahead",
            faces=[
                TestFace(1, 0.0, 20.0, (200, 540), (1720, 540), (220, 220), 0.94, 'linear', (255, 150, 150))
            ],
            target_features=["predictive_tracking", "velocity_calculation", "segment_planning"]
        ))
        
        # Scenario 3: Stability Test
        scenarios.append(VideoScenario(
            name="stability_test",
            duration=15.0,
            description="Faces entering/exiting to test assignment consistency",
            faces=[
                # Stable face throughout
                TestFace(1, 0.0, 15.0, (960, 400), (960, 400), (200, 200), 0.95, 'stationary', (255, 100, 100)),
                
                # Face enters and exits
                TestFace(2, 3.0, 12.0, (1600, 700), (600, 700), (180, 180), 0.88, 'linear', (100, 255, 100)),
                
                # Brief appearance
                TestFace(3, 6.0, 9.0, (400, 300), (400, 300), (160, 160), 0.82, 'stationary', (100, 100, 255))
            ],
            target_features=["assignment_consistency", "face_stability", "layout_stability"]
        ))
        
        # Scenario 4: Real-world Simulation
        scenarios.append(VideoScenario(
            name="realworld_simulation",
            duration=45.0,
            description="Mimics typical video call scenarios with natural movements",
            faces=[
                # Main speaker with slight movement
                TestFace(1, 0.0, 45.0, (800, 400), (1100, 450), (250, 250), 0.93, 'circular', (255, 120, 120)),
                
                # Second participant joins
                TestFace(2, 8.0, 45.0, (1600, 700), (1200, 650), (200, 200), 0.90, 'bounce', (120, 255, 120)),
                
                # Third participant brief appearance
                TestFace(3, 15.0, 30.0, (400, 300), (600, 350), (180, 180), 0.86, 'linear', (120, 120, 255)),
                
                # Fourth participant joins late
                TestFace(4, 25.0, 45.0, (1400, 300), (1300, 280), (170, 170), 0.84, 'stationary', (255, 255, 120))
            ],
            target_features=["real_world_patterns", "natural_movements", "complex_scenarios"]
        ))
        
        return scenarios

    def _generate_scenario_video(self, scenario: VideoScenario) -> str:
        """Generate a single scenario video"""
        output_path = self.output_dir / f"{scenario.name}.mp4"
        json_path = self.output_dir / f"{scenario.name}_faces.json"
        
        # Video writer setup
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, self.fps, (self.width, self.height))
        
        # Face detection data storage
        face_data_sequence = []
        
        total_frames = int(scenario.duration * self.fps)
        
        for frame_idx in range(total_frames):
            timestamp = frame_idx / self.fps
            
            # Create frame
            frame = self._create_frame_background()
            
            # Get active faces for this timestamp
            active_faces = self._get_active_faces(scenario.faces, timestamp)
            
            # Generate face detection data
            face_detections = []
            for face in active_faces:
                pos = self._calculate_face_position(face, timestamp)
                detection = self._create_face_detection(face, pos, timestamp)
                face_detections.append(detection)
                
                # Draw face on frame
                self._draw_face(frame, detection, face.color)
            
            # Process with face positioning engine
            if face_detections:
                layout = self.face_engine.calculate_face_positioning(
                    faces=face_detections,
                    frame_width=self.width,
                    frame_height=self.height,
                    target_width=self.target_width,
                    target_height=self.target_height,
                    timestamp=timestamp
                )
                
                # Draw layout visualization
                self._draw_layout_visualization(frame, layout, face_detections)
                
                # Store face data
                face_data_sequence.append({
                    'timestamp': timestamp,
                    'frame_index': frame_idx,
                    'faces': [self._face_detection_to_dict(fd) for fd in face_detections],
                    'layout': self._layout_to_dict(layout),
                    'debug_info': self.face_engine.get_debug_info()
                })
            
            # Add overlay information
            self._add_overlay_info(frame, timestamp, face_detections, 
                                 layout if face_detections else None, scenario)
            
            out.write(frame)
        
        out.release()
        
        # Save face detection data
        with open(json_path, 'w') as f:
            json.dump({
                'scenario': asdict(scenario),
                'video_info': {
                    'width': self.width,
                    'height': self.height,
                    'fps': self.fps,
                    'duration': scenario.duration,
                    'total_frames': total_frames
                },
                'face_data': face_data_sequence
            }, f, indent=2)
        
        self.logger.info(f"✅ Generated {scenario.name}: {output_path}")
        return str(output_path)

    def _create_frame_background(self) -> np.ndarray:
        """Create a frame with background"""
        frame = np.full((self.height, self.width, 3), self.colors['background'], dtype=np.uint8)

        # Add grid pattern for reference
        grid_spacing = 100
        for x in range(0, self.width, grid_spacing):
            cv2.line(frame, (x, 0), (x, self.height), (70, 70, 70), 1)
        for y in range(0, self.height, grid_spacing):
            cv2.line(frame, (0, y), (self.width, y), (70, 70, 70), 1)

        return frame

    def _get_active_faces(self, faces: List[TestFace], timestamp: float) -> List[TestFace]:
        """Get faces that should be active at the given timestamp"""
        return [face for face in faces if face.start_time <= timestamp <= face.end_time]

    def _calculate_face_position(self, face: TestFace, timestamp: float) -> Tuple[int, int]:
        """Calculate face position based on movement type and timestamp"""
        progress = (timestamp - face.start_time) / (face.end_time - face.start_time)
        progress = max(0.0, min(1.0, progress))

        if face.movement_type == 'stationary':
            return face.start_pos

        elif face.movement_type == 'linear':
            x = int(face.start_pos[0] + (face.end_pos[0] - face.start_pos[0]) * progress)
            y = int(face.start_pos[1] + (face.end_pos[1] - face.start_pos[1]) * progress)
            return (x, y)

        elif face.movement_type == 'circular':
            center_x = (face.start_pos[0] + face.end_pos[0]) // 2
            center_y = (face.start_pos[1] + face.end_pos[1]) // 2
            radius = 50
            angle = progress * 2 * np.pi
            x = int(center_x + radius * np.cos(angle))
            y = int(center_y + radius * np.sin(angle))
            return (x, y)

        elif face.movement_type == 'bounce':
            # Bounce back and forth
            bounce_progress = abs(np.sin(progress * np.pi * 2))
            x = int(face.start_pos[0] + (face.end_pos[0] - face.start_pos[0]) * bounce_progress)
            y = int(face.start_pos[1] + (face.end_pos[1] - face.start_pos[1]) * bounce_progress)
            return (x, y)

        return face.start_pos

    def _create_face_detection(self, face: TestFace, pos: Tuple[int, int],
                             timestamp: float) -> FaceDetection:
        """Create a FaceDetection object from test face data"""
        x, y = pos
        width, height = face.size

        # Adjust position to center the face
        x -= width // 2
        y -= height // 2

        # Add slight random variation to simulate real detection
        noise_x = int(np.random.normal(0, 2))
        noise_y = int(np.random.normal(0, 2))
        x += noise_x
        y += noise_y

        # Ensure face stays within frame bounds
        x = max(0, min(self.width - width, x))
        y = max(0, min(self.height - height, y))

        return FaceDetection(
            x=x, y=y, width=width, height=height,
            confidence=face.confidence + np.random.normal(0, 0.02),  # Slight confidence variation
            center_x=x + width/2, center_y=y + height/2
        )

    def _draw_face(self, frame: np.ndarray, detection: FaceDetection, color: Tuple[int, int, int]):
        """Draw a face on the frame"""
        # Draw face rectangle
        cv2.rectangle(frame, (detection.x, detection.y),
                     (detection.x + detection.width, detection.y + detection.height),
                     color, 3)

        # Draw face center
        center = (int(detection.center_x), int(detection.center_y))
        cv2.circle(frame, center, 5, color, -1)

        # Draw confidence score
        conf_text = f"{detection.confidence:.2f}"
        cv2.putText(frame, conf_text, (detection.x, detection.y - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    def _draw_layout_visualization(self, frame: np.ndarray, layout, face_detections: List[FaceDetection]):
        """Draw layout visualization on frame"""
        # Draw crop area
        crop_rect = (layout.crop_x, layout.crop_y, self.target_width, self.target_height)
        cv2.rectangle(frame, (crop_rect[0], crop_rect[1]),
                     (crop_rect[0] + crop_rect[2], crop_rect[1] + crop_rect[3]),
                     self.colors['crop_box'], 3)

        # Draw layout regions within crop area
        for region in layout.regions:
            region_x = layout.crop_x + region.x
            region_y = layout.crop_y + region.y
            cv2.rectangle(frame, (region_x, region_y),
                         (region_x + region.width, region_y + region.height),
                         self.colors['region_box'], 2)

            # Label region
            cv2.putText(frame, region.region_id, (region_x + 10, region_y + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['region_box'], 2)

        # Draw face assignments
        for face_idx, region_id in layout.face_assignments.items():
            if face_idx < len(face_detections):
                face = face_detections[face_idx]
                region = next((r for r in layout.regions if r.region_id == region_id), None)
                if region:
                    # Draw line from face to assigned region center
                    face_center = (int(face.center_x), int(face.center_y))
                    region_center = (layout.crop_x + int(region.center_x),
                                   layout.crop_y + int(region.center_y))
                    cv2.line(frame, face_center, region_center, self.colors['prediction'], 2)

    def _add_overlay_info(self, frame: np.ndarray, timestamp: float,
                         face_detections: List[FaceDetection], layout, scenario: VideoScenario):
        """Add overlay information to frame"""
        y_offset = 30
        line_height = 25

        # Scenario info
        cv2.putText(frame, f"Scenario: {scenario.name}", (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.colors['text'], 2)
        y_offset += line_height

        # Timestamp
        cv2.putText(frame, f"Time: {timestamp:.2f}s", (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
        y_offset += line_height

        # Face count
        cv2.putText(frame, f"Faces: {len(face_detections)}", (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
        y_offset += line_height

        if layout:
            # Layout type
            cv2.putText(frame, f"Layout: {layout.layout_type.value}", (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
            y_offset += line_height

            # Confidence
            cv2.putText(frame, f"Confidence: {layout.confidence:.3f}", (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
            y_offset += line_height

            # Crop position
            cv2.putText(frame, f"Crop: ({layout.crop_x}, {layout.crop_y})", (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
            y_offset += line_height

            # Predictive info
            if layout.predictive_data:
                avg_stability = sum(p.stability_score for p in layout.predictive_data) / len(layout.predictive_data)
                cv2.putText(frame, f"Avg Stability: {avg_stability:.3f}", (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['text'], 2)
                y_offset += line_height

            # Segment plan info
            if layout.segment_plan:
                cv2.putText(frame, f"Segment: {layout.segment_plan.start_time:.1f}s-{layout.segment_plan.end_time:.1f}s",
                           (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, self.colors['text'], 2)
                y_offset += line_height

    def _face_detection_to_dict(self, detection: FaceDetection) -> Dict[str, Any]:
        """Convert FaceDetection to dictionary for JSON serialization"""
        return {
            'x': detection.x,
            'y': detection.y,
            'width': detection.width,
            'height': detection.height,
            'confidence': detection.confidence,
            'center_x': detection.center_x,
            'center_y': detection.center_y
        }

    def _layout_to_dict(self, layout) -> Dict[str, Any]:
        """Convert FacePositionLayout to dictionary for JSON serialization"""
        return {
            'layout_type': layout.layout_type.value,
            'crop_x': layout.crop_x,
            'crop_y': layout.crop_y,
            'confidence': layout.confidence,
            'regions': [
                {
                    'region_id': r.region_id,
                    'x': r.x,
                    'y': r.y,
                    'width': r.width,
                    'height': r.height,
                    'center_x': r.center_x,
                    'center_y': r.center_y
                } for r in layout.regions
            ],
            'face_assignments': layout.face_assignments,
            'segment_plan': {
                'start_time': layout.segment_plan.start_time,
                'end_time': layout.segment_plan.end_time,
                'layout_type': layout.segment_plan.layout_type.value,
                'expected_face_count': layout.segment_plan.expected_face_count,
                'stability_score': layout.segment_plan.stability_score,
                'confidence': layout.segment_plan.confidence
            } if layout.segment_plan else None,
            'predictive_data': [
                {
                    'stability_score': p.stability_score,
                    'velocity': p.velocity,
                    'predicted_positions_count': len(p.predicted_positions)
                } for p in layout.predictive_data
            ] if layout.predictive_data else []
        }

    def _generate_comparison_videos(self, generated_videos: Dict[str, str]):
        """Generate side-by-side comparison videos"""
        for scenario_name, video_path in generated_videos.items():
            self.logger.info(f"🔄 Creating comparison video for {scenario_name}")

            # Load original video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error(f"Failed to open video: {video_path}")
                continue

            # Setup comparison video writer
            comparison_path = self.output_dir / f"{scenario_name}_comparison.mp4"
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            comparison_width = self.width + self.target_width + 20  # Original + cropped + padding
            comparison_height = max(self.height, self.target_height)
            out = cv2.VideoWriter(str(comparison_path), fourcc, self.fps,
                                (comparison_width, comparison_height))

            # Load face data
            json_path = self.output_dir / f"{scenario_name}_faces.json"
            with open(json_path, 'r') as f:
                data = json.load(f)

            frame_idx = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # Create comparison frame
                comparison_frame = np.zeros((comparison_height, comparison_width, 3), dtype=np.uint8)

                # Place original frame on left
                comparison_frame[:self.height, :self.width] = frame

                # Get layout data for this frame
                if frame_idx < len(data['face_data']):
                    frame_data = data['face_data'][frame_idx]
                    layout_data = frame_data['layout']

                    # Extract and place cropped region on right
                    crop_x = layout_data['crop_x']
                    crop_y = layout_data['crop_y']

                    # Ensure crop coordinates are within bounds
                    crop_x = max(0, min(self.width - self.target_width, crop_x))
                    crop_y = max(0, min(self.height - self.target_height, crop_y))

                    # Extract cropped region with bounds checking
                    crop_end_y = min(crop_y + self.target_height, self.height)
                    crop_end_x = min(crop_x + self.target_width, self.width)
                    actual_crop_height = crop_end_y - crop_y
                    actual_crop_width = crop_end_x - crop_x

                    cropped_region = frame[crop_y:crop_end_y, crop_x:crop_end_x]

                    # Place cropped region on right side
                    start_x = self.width + 10
                    start_y = (comparison_height - self.target_height) // 2

                    # Handle case where cropped region is smaller than target
                    if actual_crop_height == self.target_height and actual_crop_width == self.target_width:
                        comparison_frame[start_y:start_y + self.target_height,
                                       start_x:start_x + self.target_width] = cropped_region
                    else:
                        # Create a black background and place the cropped region in the center
                        target_region = np.zeros((self.target_height, self.target_width, 3), dtype=np.uint8)
                        offset_y = (self.target_height - actual_crop_height) // 2
                        offset_x = (self.target_width - actual_crop_width) // 2
                        target_region[offset_y:offset_y + actual_crop_height,
                                    offset_x:offset_x + actual_crop_width] = cropped_region
                        comparison_frame[start_y:start_y + self.target_height,
                                       start_x:start_x + self.target_width] = target_region

                    # Add labels
                    cv2.putText(comparison_frame, "Original (1920x1080)", (10, 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
                    cv2.putText(comparison_frame, "Cropped (720x1280)", (start_x, 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)

                    # Add layout info
                    cv2.putText(comparison_frame, f"Layout: {layout_data['layout_type']}",
                               (start_x, start_y - 40),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
                    cv2.putText(comparison_frame, f"Confidence: {layout_data['confidence']:.3f}",
                               (start_x, start_y - 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)

                out.write(comparison_frame)
                frame_idx += 1

            cap.release()
            out.release()
            self.logger.info(f"✅ Generated comparison: {comparison_path}")

    def generate_performance_report(self, generated_videos: Dict[str, str]) -> str:
        """Generate a performance analysis report"""
        report_path = self.output_dir / "performance_report.json"

        report_data = {
            'generation_timestamp': time.time(),
            'scenarios': {},
            'summary': {}
        }

        total_frames = 0
        total_duration = 0

        for scenario_name, video_path in generated_videos.items():
            json_path = self.output_dir / f"{scenario_name}_faces.json"

            if not os.path.exists(json_path):
                continue

            with open(json_path, 'r') as f:
                data = json.load(f)

            scenario_data = data['scenario']
            face_data = data['face_data']

            # Analyze scenario
            scenario_analysis = {
                'duration': scenario_data['duration'],
                'total_frames': len(face_data),
                'target_features': scenario_data['target_features'],
                'layout_transitions': 0,
                'average_confidence': 0,
                'stability_scores': [],
                'face_count_distribution': {}
            }

            prev_layout_type = None
            confidences = []

            for frame_data in face_data:
                layout = frame_data['layout']

                # Count layout transitions
                if prev_layout_type and prev_layout_type != layout['layout_type']:
                    scenario_analysis['layout_transitions'] += 1
                prev_layout_type = layout['layout_type']

                # Collect confidence scores
                confidences.append(layout['confidence'])

                # Face count distribution
                face_count = len(frame_data['faces'])
                scenario_analysis['face_count_distribution'][face_count] = \
                    scenario_analysis['face_count_distribution'].get(face_count, 0) + 1

                # Stability scores
                if layout['predictive_data']:
                    for pred_data in layout['predictive_data']:
                        scenario_analysis['stability_scores'].append(pred_data['stability_score'])

            scenario_analysis['average_confidence'] = sum(confidences) / len(confidences) if confidences else 0
            scenario_analysis['average_stability'] = sum(scenario_analysis['stability_scores']) / len(scenario_analysis['stability_scores']) if scenario_analysis['stability_scores'] else 0

            report_data['scenarios'][scenario_name] = scenario_analysis
            total_frames += scenario_analysis['total_frames']
            total_duration += scenario_analysis['duration']

        # Generate summary
        report_data['summary'] = {
            'total_scenarios': len(generated_videos),
            'total_duration': total_duration,
            'total_frames': total_frames,
            'average_fps': total_frames / total_duration if total_duration > 0 else 0,
            'video_files_generated': len(generated_videos) * 2,  # Original + comparison
            'output_directory': str(self.output_dir)
        }

        with open(report_path, 'w') as f:
            json.dump(report_data, f, indent=2)

        self.logger.info(f"📊 Performance report generated: {report_path}")
        return str(report_path)


def main():
    """Main function for video test generation"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Generate comprehensive test videos for enhanced face positioning system"
    )
    parser.add_argument(
        "--output-dir",
        default="test_videos",
        help="Output directory for generated videos (default: test_videos)"
    )
    parser.add_argument(
        "--scenario",
        choices=["layout_transitions", "predictive_tracking", "stability_test", "realworld_simulation", "all"],
        default="all",
        help="Specific scenario to generate (default: all)"
    )
    parser.add_argument(
        "--no-comparison",
        action="store_true",
        help="Skip generation of comparison videos"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger = logging.getLogger(__name__)
    logger.info("🎬 Starting Enhanced Face Positioning Video Test Generator")

    try:
        # Create generator
        generator = VideoTestGenerator(output_dir=args.output_dir)

        if args.scenario == "all":
            # Generate all scenarios
            generated_videos = generator.generate_all_test_videos()
        else:
            # Generate specific scenario
            scenarios = generator._create_test_scenarios()
            target_scenario = next((s for s in scenarios if s.name == args.scenario), None)

            if not target_scenario:
                logger.error(f"Scenario '{args.scenario}' not found")
                return 1

            logger.info(f"📹 Generating single scenario: {args.scenario}")
            video_path = generator._generate_scenario_video(target_scenario)
            generated_videos = {args.scenario: video_path}

            if not args.no_comparison:
                generator._generate_comparison_videos(generated_videos)

        # Generate performance report
        report_path = generator.generate_performance_report(generated_videos)

        # Summary
        logger.info("\n" + "="*60)
        logger.info("🎉 VIDEO GENERATION COMPLETE")
        logger.info("="*60)
        logger.info(f"📁 Output directory: {args.output_dir}")
        logger.info(f"🎬 Videos generated: {len(generated_videos)}")
        logger.info(f"📊 Performance report: {report_path}")

        logger.info("\n📹 Generated Videos:")
        for scenario_name, video_path in generated_videos.items():
            logger.info(f"   • {scenario_name}: {video_path}")
            if not args.no_comparison:
                comparison_path = video_path.replace('.mp4', '_comparison.mp4')
                logger.info(f"     Comparison: {comparison_path}")

        logger.info("\n🎯 Test Scenarios Coverage:")
        scenarios = generator._create_test_scenarios()
        for scenario in scenarios:
            if args.scenario == "all" or scenario.name == args.scenario:
                logger.info(f"   • {scenario.name}: {scenario.description}")
                logger.info(f"     Features: {', '.join(scenario.target_features)}")

        logger.info("\n✅ All test videos generated successfully!")
        logger.info("Use these videos to validate the enhanced face positioning system.")

        return 0

    except Exception as e:
        logger.error(f"❌ Video generation failed: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
