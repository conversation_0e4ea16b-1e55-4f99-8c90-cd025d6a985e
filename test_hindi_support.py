#!/usr/bin/env python3
"""
Test script for Hindi language support in VideoHighlightsDetector

This script comprehensively tests the multilingual functionality including:
- Language detection accuracy
- Model loading and fallbacks
- Hindi-specific pattern recognition
- Performance comparisons
- Edge case handling
"""

import sys
import os
import time
import logging
from typing import List, Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Test data
HINDI_TRANSCRIPT_SEGMENTS = [
    {
        "text": "नमस्कार दोस्तों! आज मैं आपको एक अविश्वसनीय कहानी सुनाने जा रहा हूँ।",
        "start": 0.0,
        "end": 4.5
    },
    {
        "text": "यह रहस्य बहुत ही चौंकाने वाला है और ज्यादातर लोग नहीं जानते।",
        "start": 4.5,
        "end": 8.2
    },
    {
        "text": "अनुसंधान दिखाता है कि यह बात सच्चाई है। वाकई अद्भुत!",
        "start": 8.2,
        "end": 12.0
    },
    {
        "text": "तब जाकर मुझे एहसास हुआ कि यह जीवन बदलने वाला मोड़ था।",
        "start": 12.0,
        "end": 16.5
    },
    {
        "text": "क्या होगा अगर मैं आपको बताऊं कि यह सब कैसे हुआ? सोचिए!",
        "start": 16.5,
        "end": 21.0
    }
]

ENGLISH_TRANSCRIPT_SEGMENTS = [
    {
        "text": "Hello everyone! Today I'm going to tell you an incredible story.",
        "start": 0.0,
        "end": 4.5
    },
    {
        "text": "This secret is absolutely shocking and most people don't know about it.",
        "start": 4.5,
        "end": 8.2
    },
    {
        "text": "Research shows that this is the truth. Really amazing!",
        "start": 8.2,
        "end": 12.0
    },
    {
        "text": "That's when I realized this was a life changing turning point.",
        "start": 12.0,
        "end": 16.5
    },
    {
        "text": "What if I told you how this all happened? Just imagine!",
        "start": 16.5,
        "end": 21.0
    }
]

MIXED_TRANSCRIPT_SEGMENTS = [
    {
        "text": "Hello दोस्तों! Today मैं आपको एक amazing कहानी बताऊंगा।",
        "start": 0.0,
        "end": 4.5
    },
    {
        "text": "This is बहुत ही interesting और shocking भी है।",
        "start": 4.5,
        "end": 8.2
    }
]

def test_language_detection():
    """Test automatic language detection functionality"""
    print("\n" + "="*60)
    print("TESTING LANGUAGE DETECTION")
    print("="*60)
    
    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector
        
        # Test Hindi detection
        detector_hindi = VideoHighlightsDetector(language="auto")
        detector_hindi._detect_content_language(HINDI_TRANSCRIPT_SEGMENTS)
        
        print(f"Hindi Content Detection:")
        print(f"  Detected Language: {detector_hindi.detected_language}")
        print(f"  Confidence: {detector_hindi.language_confidence:.2f}")
        
        # Test English detection
        detector_english = VideoHighlightsDetector(language="auto")
        detector_english._detect_content_language(ENGLISH_TRANSCRIPT_SEGMENTS)
        
        print(f"\nEnglish Content Detection:")
        print(f"  Detected Language: {detector_english.detected_language}")
        print(f"  Confidence: {detector_english.language_confidence:.2f}")
        
        # Test mixed content
        detector_mixed = VideoHighlightsDetector(language="auto")
        detector_mixed._detect_content_language(MIXED_TRANSCRIPT_SEGMENTS)
        
        print(f"\nMixed Content Detection:")
        print(f"  Detected Language: {detector_mixed.detected_language}")
        print(f"  Confidence: {detector_mixed.language_confidence:.2f}")
        
        return True
        
    except Exception as e:
        print(f"Language detection test failed: {e}")
        return False

def test_model_loading():
    """Test multilingual model loading and fallbacks"""
    print("\n" + "="*60)
    print("TESTING MODEL LOADING")
    print("="*60)
    
    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector
        
        # Test Hindi model preferences
        detector_hindi = VideoHighlightsDetector(language="hindi")
        detector_hindi.detected_language = "hindi"
        
        print("Testing Hindi Model Loading:")
        
        # Test spaCy model
        spacy_model = detector_hindi._get_spacy_model()
        print(f"  spaCy Model: {'Loaded' if spacy_model else 'Fallback'}")
        
        # Test emotion model
        emotion_model = detector_hindi._get_emotion_model()
        print(f"  Emotion Model: {'Loaded' if emotion_model else 'Fallback'}")
        
        # Test sentiment model
        sentiment_model = detector_hindi._get_sentiment_model()
        print(f"  Sentiment Model: {'Loaded' if sentiment_model else 'Fallback'}")
        
        # Test embedding model
        embedding_model = detector_hindi._get_embedding_model()
        print(f"  Embedding Model: {'Loaded' if embedding_model else 'Fallback'}")
        
        # Test English model preferences
        detector_english = VideoHighlightsDetector(language="english")
        detector_english.detected_language = "english"
        
        print("\nTesting English Model Loading:")
        
        spacy_model_en = detector_english._get_spacy_model()
        print(f"  spaCy Model: {'Loaded' if spacy_model_en else 'Fallback'}")
        
        emotion_model_en = detector_english._get_emotion_model()
        print(f"  Emotion Model: {'Loaded' if emotion_model_en else 'Fallback'}")
        
        return True
        
    except Exception as e:
        print(f"Model loading test failed: {e}")
        return False

def test_hindi_text_processing():
    """Test Hindi-specific text processing features"""
    print("\n" + "="*60)
    print("TESTING HINDI TEXT PROCESSING")
    print("="*60)
    
    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector
        
        detector = VideoHighlightsDetector(language="hindi")
        detector.detected_language = "hindi"
        
        # Test Hindi sentence splitting
        hindi_text_with_danda = "यह पहला वाक्य है। यह दूसरा वाक्य है॥ यह तीसरा वाक्य है।"
        sentences = detector._hindi_sentence_splitting(hindi_text_with_danda)
        
        print("Hindi Sentence Splitting Test:")
        print(f"  Input: {hindi_text_with_danda}")
        print(f"  Sentences: {sentences}")
        print(f"  Count: {len(sentences)}")
        
        # Test Hindi engagement patterns
        hindi_engagement_text = "यह अविश्वसनीय है! वाकई बहुत अद्भुत। अनुसंधान दिखाता है।"
        
        # Test burstiness features
        burstiness = detector._extract_burstiness_features(hindi_engagement_text)
        print(f"\nHindi Burstiness Analysis:")
        print(f"  Text: {hindi_engagement_text}")
        print(f"  Overall Burstiness: {burstiness['overall_burstiness']:.3f}")
        print(f"  Punctuation Density: {burstiness['punctuation_density']:.3f}")
        
        # Test fallback emotion detection
        emotions = detector._fallback_emotion_detection(hindi_engagement_text)
        print(f"\nHindi Emotion Detection:")
        print(f"  Total Intensity: {emotions['total_intensity']:.3f}")
        for emotion, score in emotions.items():
            if emotion != 'total_intensity' and score > 0:
                print(f"  {emotion}: {score:.3f}")
        
        return True

    except Exception as e:
        print(f"Hindi text processing test failed: {e}")
        return False

def test_feature_weights():
    """Test language-specific feature weight adjustments"""
    print("\n" + "="*60)
    print("TESTING FEATURE WEIGHTS")
    print("="*60)

    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector

        # Test Hindi weights
        detector_hindi = VideoHighlightsDetector(content_type="general", language="hindi")
        hindi_weights = detector_hindi.feature_weights

        print("Hindi Feature Weights:")
        for feature, weight in hindi_weights.items():
            print(f"  {feature}: {weight:.3f}")

        # Test English weights
        detector_english = VideoHighlightsDetector(content_type="general", language="english")
        english_weights = detector_english.feature_weights

        print("\nEnglish Feature Weights:")
        for feature, weight in english_weights.items():
            print(f"  {feature}: {weight:.3f}")

        # Verify weights sum to 1.0
        hindi_sum = sum(hindi_weights.values())
        english_sum = sum(english_weights.values())

        print(f"\nWeight Validation:")
        print(f"  Hindi weights sum: {hindi_sum:.3f}")
        print(f"  English weights sum: {english_sum:.3f}")
        print(f"  Hindi valid: {abs(hindi_sum - 1.0) < 0.001}")
        print(f"  English valid: {abs(english_sum - 1.0) < 0.001}")

        return True

    except Exception as e:
        print(f"Feature weights test failed: {e}")
        return False

def test_performance_comparison():
    """Test performance differences between Hindi and English processing"""
    print("\n" + "="*60)
    print("TESTING PERFORMANCE COMPARISON")
    print("="*60)

    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector

        # Test Hindi processing time
        start_time = time.time()
        detector_hindi = VideoHighlightsDetector(language="hindi")
        hindi_highlights = detector_hindi.find_best_highlights(
            HINDI_TRANSCRIPT_SEGMENTS,
            max_highlights=3
        )
        hindi_time = time.time() - start_time

        print(f"Hindi Processing:")
        print(f"  Time: {hindi_time:.3f} seconds")
        print(f"  Highlights found: {len(hindi_highlights)}")
        print(f"  Cache hits: {detector_hindi.performance_metrics['cache_hits']}")
        print(f"  Cache misses: {detector_hindi.performance_metrics['cache_misses']}")

        # Test English processing time
        start_time = time.time()
        detector_english = VideoHighlightsDetector(language="english")
        english_highlights = detector_english.find_best_highlights(
            ENGLISH_TRANSCRIPT_SEGMENTS,
            max_highlights=3
        )
        english_time = time.time() - start_time

        print(f"\nEnglish Processing:")
        print(f"  Time: {english_time:.3f} seconds")
        print(f"  Highlights found: {len(english_highlights)}")
        print(f"  Cache hits: {detector_english.performance_metrics['cache_hits']}")
        print(f"  Cache misses: {detector_english.performance_metrics['cache_misses']}")

        print(f"\nPerformance Comparison:")
        print(f"  Time ratio (Hindi/English): {hindi_time/english_time:.2f}")

        return True

    except Exception as e:
        print(f"Performance comparison test failed: {e}")
        return False

def test_highlight_quality():
    """Test the quality and relevance of generated highlights"""
    print("\n" + "="*60)
    print("TESTING HIGHLIGHT QUALITY")
    print("="*60)

    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector

        # Test Hindi highlights
        detector_hindi = VideoHighlightsDetector(language="auto")
        hindi_highlights = detector_hindi.find_best_highlights(
            HINDI_TRANSCRIPT_SEGMENTS,
            max_highlights=2
        )

        print("Hindi Highlights:")
        for i, highlight in enumerate(hindi_highlights):
            print(f"  Highlight {i+1}:")
            print(f"    Text: {highlight['text'][:100]}...")
            print(f"    Score: {highlight['engagement_score']:.3f}")
            print(f"    Duration: {highlight['duration']:.1f}s")
            print(f"    Language: {detector_hindi.detected_language}")

        # Test English highlights
        detector_english = VideoHighlightsDetector(language="auto")
        english_highlights = detector_english.find_best_highlights(
            ENGLISH_TRANSCRIPT_SEGMENTS,
            max_highlights=2
        )

        print(f"\nEnglish Highlights:")
        for i, highlight in enumerate(english_highlights):
            print(f"  Highlight {i+1}:")
            print(f"    Text: {highlight['text'][:100]}...")
            print(f"    Score: {highlight['engagement_score']:.3f}")
            print(f"    Duration: {highlight['duration']:.1f}s")
            print(f"    Language: {detector_english.detected_language}")

        return True

    except Exception as e:
        print(f"Highlight quality test failed: {e}")
        return False

def test_edge_cases():
    """Test edge cases and error handling"""
    print("\n" + "="*60)
    print("TESTING EDGE CASES")
    print("="*60)

    try:
        from highlight_extraction.utils.video_detector import VideoHighlightsDetector

        # Test empty input
        detector = VideoHighlightsDetector(language="auto")
        empty_highlights = detector.find_best_highlights([])
        print(f"Empty input test: {len(empty_highlights)} highlights (expected: 0)")

        # Test invalid input
        invalid_segments = [{"invalid": "data"}]
        invalid_highlights = detector.find_best_highlights(invalid_segments)
        print(f"Invalid input test: {len(invalid_highlights)} highlights")

        # Test very short segments
        short_segments = [
            {"text": "Hi", "start": 0.0, "end": 0.5},
            {"text": "नमस्ते", "start": 0.5, "end": 1.0}
        ]
        short_highlights = detector.find_best_highlights(short_segments)
        print(f"Short segments test: {len(short_highlights)} highlights")

        # Test language detection fallback
        detector_fallback = VideoHighlightsDetector(language="auto")
        detector_fallback.detected_language = None
        fallback_weights = detector_fallback._get_content_weights("general", "auto")
        print(f"Fallback weights test: {len(fallback_weights)} features")

        return True

    except Exception as e:
        print(f"Edge cases test failed: {e}")
        return False

def run_all_tests():
    """Run all test functions and provide summary"""
    print("STARTING COMPREHENSIVE HINDI LANGUAGE SUPPORT TESTS")
    print("="*80)

    test_results = {}

    # Run all tests
    test_functions = [
        ("Language Detection", test_language_detection),
        ("Model Loading", test_model_loading),
        ("Hindi Text Processing", test_hindi_text_processing),
        ("Feature Weights", test_feature_weights),
        ("Performance Comparison", test_performance_comparison),
        ("Highlight Quality", test_highlight_quality),
        ("Edge Cases", test_edge_cases)
    ]

    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results[test_name] = "PASSED" if result else "FAILED"
        except Exception as e:
            test_results[test_name] = f"ERROR: {str(e)}"

    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results.items():
        status_symbol = "✓" if result == "PASSED" else "✗"
        print(f"{status_symbol} {test_name}: {result}")
        if result == "PASSED":
            passed += 1

    print(f"\nOverall Result: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")

    return test_results

if __name__ == "__main__":
    run_all_tests()
